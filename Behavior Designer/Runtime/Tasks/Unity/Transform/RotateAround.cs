using UnityEngine;

namespace BehaviorDesigner.Runtime.Tasks.Unity.UnityTransform
{
    [TaskCategory("Unity/Transform")]
    [TaskDescription("Applies a rotation. Returns Success.")]
    public class RotateAround : Action
    {
        [Tooltip("The GameObject that the task operates on. If null the task GameObject is used.")]
        public SharedGameObject targetGameObject;
        [<PERSON><PERSON><PERSON>("Point to rotate around")]
        public SharedVector3 point;
        [<PERSON><PERSON><PERSON>("Axis to rotate around")]
        public SharedVector3 axis;
        [Tooltip("Amount to rotate")]
        public SharedFloat angle;

        private Transform targetTransform;
        private GameObject prevGameObject;

        public override void OnStart()
        {
            var currentGameObject = GetDefaultGameObject(targetGameObject.Value);
            if (currentGameObject != prevGameObject) {
                targetTransform = currentGameObject.GetComponent<Transform>();
                prevGameObject = currentGameObject;
            }
        }

        public override TaskStatus OnUpdate()
        {
            if (targetTransform == null) {
                Debug.LogWarning("Transform is null");
                return TaskStatus.Failure;
            }

            targetTransform.RotateAround(point.Value, axis.Value, angle.Value);

            return TaskStatus.Success;
        }

        public override void OnReset()
        {
            targetGameObject = null;
            point = Vector3.zero;
            axis = Vector3.zero;
            angle = 0;
        }
    }
}