// Copyright Isto Inc.

using Photon.Pun;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    public abstract class PUN2Behaviour : MonoBehaviourPun
    {
        // UNITY HOOKUP


        // OTHER FIELDS


        // INJECTION

        protected PUN2NetworkManager _sessionManager;

        [Inject]
        public void Inject(PUN2NetworkManager sessionManager)
        {
            _sessionManager = sessionManager;
        }


        // LIFECYCLE EVENTS


        // EVENT HANDLING

        protected virtual void RegisterEvents()
        {

        }

        protected virtual void UnregisterEvents()
        {

        }


        // OTHER METHODS

        [ContextMenu("TestCustomRPC")]
        private void TestCustomRPC()
        {
            SendObjectMasterEvent(NetworkedObjectEventEnum.NONE);
            SendObjectEvent(NetworkedObjectEventEnum.NONE, 1);
        }

        // Handles sending network messages to all instances of the object, remote or local.
        // Anything that is simply information or is more time sensitive could go through here, although for seriously
        // time critical methods (either because of urgency or because order matters) this is not good enough.
        protected void SendObjectEvent(NetworkedObjectEventEnum id, params object[] parameters)
        {
            if (id == null || id == NetworkedObjectEventEnum.NONE)
            {
                Debug.LogError($"Missing {nameof(NetworkedObjectEventEnum)} value in " +
                               $"{nameof(PUN2Behaviour)}.{nameof(SendObjectEvent)}");
                return;
            }

            if (_sessionManager.IsSinglePlayerTesting)
            {
                RPC_ObjectEvent(id.Value, parameters);
            }
            else
            {
                photonView.RPC(nameof(RPC_ObjectEvent), RpcTarget.All, id.Value, parameters);
            }
        }

        // Handles sending network messages to the object's controller.
        // Anything that changes the object's state or requires some decision making probably should go through here.
        protected void SendObjectMasterEvent(NetworkedObjectEventEnum id, params object[] parameters)
        {
            if (id == null || id == NetworkedObjectEventEnum.NONE)
            {
                Debug.LogError($"Missing {nameof(NetworkedObjectEventEnum)} value in " +
                               $"{nameof(PUN2Behaviour)}.{nameof(SendObjectMasterEvent)}");
                return;
            }

            if (_sessionManager.IsSinglePlayerTesting)
            {
                RPC_ObjectEvent(id.Value, parameters);
            }
            else
            {
                photonView.RPC(nameof(RPC_ObjectEvent), photonView.Owner, id.Value, parameters);
            }
        }

        // Note: calling the same RPC for both SendObjectEvent and SendObjectMasterEvent might be a step too far.
        // Not sure. Consider splitting it up.
        [PunRPC]
        public void RPC_ObjectEvent(byte eventID, params object[] parameters)
        {
            NetworkedObjectEventEnum id = NetworkedObjectEventEnum.GetByValue(eventID);

            // Just for testing/demo purposes
            Debug.Log($"event {id.Name} received by {this.gameObject.name}", this.gameObject);

            HandleObjectEvent(id, parameters);
        }

        // Override this to handle your custom values of NetworkedObjectEventEnum
        public virtual void HandleObjectEvent(NetworkedObjectEventEnum id, params object[] parameters)
        {
            Debug.LogError(nameof(RPC_ObjectEvent) + " unhandled enum value: " + id.Name, gameObject);
        }
    }
}