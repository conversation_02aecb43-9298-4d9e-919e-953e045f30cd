// Copyright Isto Inc.

using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Photon.Pun;
using UnityEngine;

namespace Isto.Core.Photon
{
    public class SimplePUN2PlayerController : PUN2PlayerController
    {
        // PROPERTIES

        protected override Collider MainHitBox => null;

        public override State MovementState => startState;

        public override bool IsMoving => throw new System.NotImplementedException();


        // LIFECYCLE EVENTS

        protected override void Update()
        {
            if (!PhotonView.IsMine)
                return;

            base.Update();
        }

        // OTHER METHODS

        public Vector2 GetInputs()
        {
            Vector2 input = Vector3.zero;
            input.x = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            input.y = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            return input;
        }

        protected override void OnPlayerTakeDamage(object sender, HealthEventArgs e)
        {
            throw new System.NotImplementedException();
        }

        protected override void OnPlayerKilled(object sender, HealthEventArgs e)
        {
            throw new System.NotImplementedException();
        }

        public override void SetMovementEnabled(bool active)
        {
            throw new System.NotImplementedException();
        }

        public override void ChangeToMoveState()
        {
            throw new System.NotImplementedException();
        }

        public override void ChangeToCustomState(State state)
        {
            throw new System.NotImplementedException();
        }
    }
}