// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.Photon
{
    [CreateAssetMenu(fileName = "SimplePUN2MoveState", menuName = "Scriptables/State/SimplePUN2MoveState")]
    public class SimplePUN2MoveState : State
    {
        private SimplePUN2PlayerController _playerController;

        public override void Enter(ScriptableStateMachine controller)
        {
            _playerController = controller as SimplePUN2PlayerController;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            _playerController = controller as SimplePUN2PlayerController;
            Vector2 input = _playerController.GetInputs();

            Vector3 movement = new Vector3(input.x, input.y, 0f) * Time.deltaTime * 20f;
            Vector3 newPosition = _playerController.GetPlayerPosition() + movement;
            newPosition.x = Mathf.Clamp(newPosition.x, -20f, 20f);
            newPosition.y = Mathf.Clamp(newPosition.y, -20f, 20f);

            _playerController.SetPlayerPosition(newPosition);

            return this;
        }

        public override void Exit(ScriptableStateMachine controller)
        {

        }
    }
}