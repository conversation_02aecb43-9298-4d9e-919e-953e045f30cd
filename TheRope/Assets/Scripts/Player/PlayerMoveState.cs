// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP
{
    [CreateAssetMenu(fileName = "PlayerMoveState", menuName = "Scriptables/State/PlayerMoveState")]
    public class PlayerMoveState : State
    {
        // Todo: proper inputs
        private const string MouseXInput = "Mouse X";
        private const string MouseYInput = "Mouse Y";
        private const string MouseScrollInput = "Mouse ScrollWheel";
        private const string HorizontalInput = "Horizontal";
        private const string VerticalInput = "Vertical";
        
        private TRPPlayerController _playerController;

        public override void Enter(ScriptableStateMachine controller)
        {
            _playerController = controller as TRPPlayerController;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            _playerController = controller as TRPPlayerController;
            // Vector2 input = _playerController.GetInputs();
            //
            // Vector3 movement = new Vector3(input.x, input.y, 0f) * Time.deltaTime * 20f;
            // Vector3 newPosition = _playerController.GetPlayerPosition() + movement;
            // newPosition.x = Mathf.Clamp(newPosition.x, -20f, 20f);
            // newPosition.y = Mathf.Clamp(newPosition.y, -20f, 20f);
            //
            // _playerController.SetPlayerPosition(newPosition);
            
            
            
            // Query Inputs
            if (_playerController.IsMine)
            {
                _playerController.CharacterInputs = GetCharacterInputs();
                _playerController.CameraInputs = GetCameraInputs();
            }


            return this;
        }

        public override void Exit(ScriptableStateMachine controller)
        {

        }
        
        
        // OTHER METHODS

        private PlayerCharacterController.PlayerCharacterInputs GetCharacterInputs()
        {
            PlayerCharacterController.PlayerCharacterInputs characterInputs = new PlayerCharacterController.PlayerCharacterInputs();
            characterInputs.MoveAxisForward = Input.GetAxisRaw(VerticalInput);
            characterInputs.MoveAxisRight = Input.GetAxisRaw(HorizontalInput);
            characterInputs.CameraRotation = _playerController.CameraRoot.rotation;//todo: camera rig/input
            characterInputs.JumpDown = Input.GetKeyDown(KeyCode.Space);
            characterInputs.Crouch = Input.GetKey(KeyCode.C);
            characterInputs.CrouchDown = Input.GetKeyDown(KeyCode.C);
            characterInputs.CrouchUp = Input.GetKeyUp(KeyCode.C);

            return characterInputs;
        }

        private PlayerCameraRigController.PlayerCameraInputs GetCameraInputs()
        {
            PlayerCameraRigController.PlayerCameraInputs cameraInputs = new PlayerCameraRigController.PlayerCameraInputs();
            float mouseLookAxisUp = Input.GetAxisRaw(MouseYInput);
            float mouseLookAxisRight = Input.GetAxisRaw(MouseXInput);
            cameraInputs.rotationInput = new Vector3(mouseLookAxisRight, mouseLookAxisUp, 0f);

            // Prevent moving the camera while the cursor isn't locked
            if (Cursor.lockState != CursorLockMode.Locked)
            {
                cameraInputs.rotationInput = Vector3.zero;
            }

            // Input for zooming the camera
            cameraInputs.zoomInput = -Input.GetAxis(MouseScrollInput);
            
            return cameraInputs;
        }
    }
}
