// Copyright Isto Inc.

using System;
using Isto.Core;
using Isto.Core.Inputs;
using Isto.Core.Photon;
using Isto.Core.StateMachine;
using Photon.Pun;
using UnityEngine;
using Zenject;

namespace Isto.TRP
{
    public class TRPPlayerController : PUN2PlayerController
    {
        
        
        // UNITY HOOKUPS

        [SerializeField] private PlayerBodyFirstPerson _playerBodyFirstPersonPrefab = null;
        [SerializeField] private PlayerBodyThirdPerson _playerBodyThirdPersonPrefab = null;
        [SerializeField] private PlayerCameraRigController _playerCameraRigControllerPrefab = null;

        [SerializeField] private PlayerCharacterController _playerCharacterController = null;
        [SerializeField] private PlayerBodyController _playerBodyController = null;
        
        
        // [Header("Debug")]


        // OTHER FIELDS

        private PlayerBodyFirstPerson _playerBodyFirstPerson = null;
        private PlayerBodyThirdPerson _playerBodyThirdPerson = null;
        private PlayerCameraRigController _playerCameraRigController = null;


        // PROPERTIES

        public bool IsMine => PhotonView.IsMine;
        public bool IsFirstPersonMode => IsMine && _playerCameraRigController.CurrentDistanceClamped < 0.01f;
        public PlayerCharacterController PlayerCharacterController => _playerCharacterController;
        public PlayerBodyController PlayerBodyController => _playerBodyController;
        public PlayerBodyFirstPerson PlayerBodyFirstPerson => _playerBodyFirstPerson;
        public PlayerBodyThirdPerson PlayerBodyThirdPerson => _playerBodyThirdPerson;
        public PlayerCameraRigController PlayerCameraRigController => _playerCameraRigController;

        public Transform CameraRoot => _playerCameraRigController.Transform;

        public override State MovementState => startState;

        public override bool IsMoving => throw new System.NotImplementedException();

        protected override Collider MainHitBox => null;


        // Inputs
        public PlayerCharacterController.PlayerCharacterInputs CharacterInputs { get; set; } = new PlayerCharacterController.PlayerCharacterInputs();
        public PlayerCameraRigController.PlayerCameraInputs CameraInputs { get; set; } = new PlayerCameraRigController.PlayerCameraInputs();


        // INJECTION


        [Inject]
        public void Inject()
        {
            
        }


        // LIFECYCLE EVENTS

        protected override void Awake()
        {
            base.Awake();
            
            InstantiateCameraRig();
            
            if (PhotonView.IsMine)
            {
                Cursor.lockState = CursorLockMode.Locked;
            }
            
            InstantiateBody();

            if (!PhotonView.IsMine)
            {
                _playerCharacterController.Motor.enabled = false;
            }
        }

        protected override void Start()
        {
            base.Start();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
        }

        private void OnDestroy()
        {
            PhotonView.Destroy(_playerCameraRigController);

            if (_playerBodyFirstPerson != null)
            {
                PhotonView.Destroy(_playerBodyFirstPerson);
            }
            if (_playerBodyThirdPerson != null)
            {
                PhotonView.Destroy(_playerBodyThirdPerson);
            }
        }

        protected override void Update()
        {
            if (!PhotonView.IsMine)
                return;

            base.Update();

            UpdateBodyVisibility();

            HandleCharacterInput();
        }

        private void LateUpdate()
        {
            HandleCameraInput();

            if (_playerBodyFirstPerson != null)
            {
                _playerBodyFirstPerson.UpdateBodyPosition();
            }

            if (_playerBodyThirdPerson != null)
            {
                _playerBodyThirdPerson.UpdateBodyPosition();
            }
            
            _playerBodyController.UpdateItemPosition();
        }


        // OTHER METHODS

        private void InstantiateCameraRig()
        {
            _playerCameraRigController = PhotonView.Instantiate(_playerCameraRigControllerPrefab, this.transform).GetComponent<PlayerCameraRigController>();
            PlayerCameraRigController.PlayerController = this;
            PlayerCameraRigController.SetFollowTransform(PlayerCharacterController.CameraFollowPoint);
        }

        private void InstantiateBody()
        {
            if (PhotonView.IsMine)
            {
                _playerBodyFirstPerson = Instantiate(_playerBodyFirstPersonPrefab, this.transform).GetComponent<PlayerBodyFirstPerson>();
                _playerBodyFirstPerson.Anchor = PlayerCameraRigController.Neck2;
            }

            _playerBodyThirdPerson = Instantiate(_playerBodyThirdPersonPrefab, PlayerCharacterController.BodyRoot).GetComponent<PlayerBodyThirdPerson>();
            _playerBodyThirdPerson.Anchor = _playerCharacterController.BodyRoot;

            UpdateBodyVisibility();
        }

        private void UpdateBodyVisibility()
        {
            if (_playerBodyFirstPerson != null)
            {
                _playerBodyFirstPerson.SetModelActive(IsFirstPersonMode);
            }
            if (_playerBodyThirdPerson != null)
            {
                _playerBodyThirdPerson.SetModelActive(!IsFirstPersonMode);
            }
        }

        public Vector2 GetInputs()
        {
            Vector2 input = Vector3.zero;
            input.x = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            input.y = _controls.GetAxis(Controls.MovementAxis.MoveVertical);

            return input;
        }

        public override void ChangeToCustomState(State state)
        {
            throw new System.NotImplementedException();
        }

        public override void ChangeToMoveState()
        {
            throw new System.NotImplementedException();
        }

        public override void SetMovementEnabled(bool active)
        {
            throw new System.NotImplementedException();
        }

        protected override void OnPlayerKilled(object sender, HealthEventArgs e)
        {
            throw new System.NotImplementedException();
        }

        protected override void OnPlayerTakeDamage(object sender, HealthEventArgs e)
        {
            throw new System.NotImplementedException();
        }
        
        private void HandleCharacterInput()
        {
            // Todo: proper inputs

            // Apply inputs to character
            PlayerCharacterController.SetInputs(CharacterInputs);
        }
        
        private void HandleCameraInput()
        {
            if (PhotonView.IsMine)
            {
                // Apply inputs to the camera
                PlayerCameraRigController.UpdateWithInput(Time.deltaTime, CameraInputs);
            }
            else
            {
                PlayerCameraRigController.UpdateWithoutInput();
            }
        }
    }
}
