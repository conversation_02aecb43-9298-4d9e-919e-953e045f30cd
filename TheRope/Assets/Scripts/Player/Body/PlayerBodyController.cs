using System;
using System.Collections.Generic;
using ExitGames.Client.Photon;
using Isto.Core.Items;
using Isto.Core.StateMachine;
using Isto.Items;
using Isto.TRP.PlayerBodyStates;
using Photon.Pun;
using Photon.Realtime;
using UnityEngine;
using Zenject;

namespace Isto.TRP
{
    public class PlayerBodyController : ScriptableStateMachine, IPunObservable, IInRoomCallbacks
    {
        public int EquipInParam { get; } = Animator.StringToHash("EquipIn");
        public int EquipOutParam { get; } = Animator.StringToHash("EquipOut");
        public const int EquipItemLayerIndex = 1;

        public const int UNEQUIP_STATE_ID = 0;
        public const int EQUIP_IN_STATE_ID = 1;
        public const int IDLE_STATE_ID = 2;
        public const int EQUIP_OUT_STATE_ID = 3;
        
        
        // UNITY HOOKUPS
        
        [Space]
        [SerializeField] private TRPPlayerController _playerController = null;

        [Header("States")]
        [SerializeField] public UnequippedState unequippedState = null;
        [SerializeField] public EquipInState equipInState = null;
        [SerializeField] public IdleState idleState = null;
        [SerializeField] public EquipOutState equipOutState = null;

        [Header("Debug")]
        [SerializeField] private EquippableItem3D _torchItem = null;
        
        
        // OTHER FIELDS
        
        private PhotonView _photonView;
        private MasterItemList _masterItemList;

        private PlayerBodyState[] _states;
        

        // PROPERTIES

        public TRPPlayerController PlayerController => _playerController;
        public PlayerBodyFirstPerson FirstPersonBody => PlayerController.PlayerBodyFirstPerson;
        public PlayerBodyThirdPerson ThirdPersonBody => PlayerController.PlayerBodyThirdPerson;

        public PhotonView PhotonView
        {
            get
            {
                if (_photonView == null)
                {
                    _photonView = PlayerController.GetComponent<PhotonView>();
                }
                return _photonView;
            }
        }
        
        private PlayerBodyState CurrentPlayerBodyState => _currentState as PlayerBodyState;


        public EquippableItem3D WaitingToEquip { get; set; } = null;
        
        public EquippableItem3D EquippedItem { get; set; } = null;
        
        public bool PlayingEquipIn { get; set; }
        public bool PlayingEquipOut { get; set; }


        public ItemProp EquippedItemProp { get; set; } = null;

        
        // INJECTION

        [Inject]
        public void Inject(MasterItemList masterItemList)
        {
            _masterItemList = masterItemList;
        }
        

        // LIFECYCLE EVENTS
        
        public void OnPlayerEnteredRoom(Player newPlayer)
        {
            if (PhotonView.IsMine)
            {
                // Send equipped item state to new player
                string itemId = EquippedItem != null ? EquippedItem.itemID : "";
                PhotonView.RPC("RpcChangedEquippedItem", newPlayer, itemId);
            }
        }

        public void OnPlayerLeftRoom(Player otherPlayer)
        {
            
        }

        public void OnRoomPropertiesUpdate(Hashtable propertiesThatChanged)
        {
            
        }

        public void OnPlayerPropertiesUpdate(Player targetPlayer, Hashtable changedProps)
        {
            
        }

        public void OnMasterClientSwitched(Player newMasterClient)
        {
            
        }

        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            
        }

        public void SendRpcChangeEquipItem(EquippableItem3D item)
        {
            string itemId = item != null ? item.itemID : "";
            PhotonView.RPC("RpcChangedEquippedItem", RpcTarget.Others, itemId);
        }

        [PunRPC]
        private void RpcChangedEquippedItem(string itemId)
        {
            EquippableItem3D item = null;

            if (!string.IsNullOrEmpty(itemId))
            {
                item = _masterItemList.GetItemByID<EquippableItem3D>(itemId);
            }

            if (item != null && EquippedItem != item)
            {
                EquipItem(item);
            }
            else if (item == null)
            {
                UnequipItem();
            }
        }
        
        
        // UNITY LIFECYCLE

        private void Awake()
        {
            _states = new[]
            {
                unequippedState as PlayerBodyState,
                equipInState,
                idleState,
                equipOutState
            };

            PhotonNetwork.AddCallbackTarget(this);
        }

        private void OnDestroy()
        {
            PhotonNetwork.RemoveCallbackTarget(this);
        }

        protected void Update()
        { 
            if (PhotonView.IsMine)
            {
                if (Input.GetKeyDown(KeyCode.T))
                {
                    if (EquippedItem == null)
                    {
                        EquipItem(_torchItem);
                    }
                    else
                    {
                        UnequipItem();
                    }
                }
            }


            // Proccess current state
            State nextState = _currentState.Run(this) as State;

            if (nextState != _currentState)
                ChangeState(nextState);

            if (EquippedItemProp != null)
            {
                if (!PhotonView.IsMine || !PlayerController.IsFirstPersonMode)
                {
                    EquippedItemProp.SetThirdPersonLayer();
                }
                else
                {
                    EquippedItemProp.SetFirstPersonLayer();
                }
            }
        }


        // OTHER METHODS

        public void EquipItem(EquippableItem3D item)
        {
            WaitingToEquip = item;
        }

        public void UnequipItem()
        {
            WaitingToEquip = null;
        }

        public void UpdateItemPosition()
        {
            if (EquippedItemProp == null)
            {
                return;
            }

            Transform[] propBones = _playerController.IsFirstPersonMode
                ? _playerController.PlayerBodyFirstPerson.propBones
                : _playerController.PlayerBodyThirdPerson.propBones;
            
            for (int i = 0; i < EquippedItemProp.propRoots.Length && i < propBones.Length; i++)
            {
                if (EquippedItemProp.propRoots[i] != null && propBones[i] != null)
                {
                    EquippedItemProp.propRoots[i].position = propBones[i].position;
                    EquippedItemProp.propRoots[i].rotation = propBones[i].rotation;
                }
            }
        }

        public void SetAnimatorController(RuntimeAnimatorController firstPersonAnimatorController, RuntimeAnimatorController thirdPersonAnimatorController)
        {
            if (FirstPersonBody != null)
            {
                FirstPersonBody.SetAnimatorController(firstPersonAnimatorController);
            }
            
            if (ThirdPersonBody != null)
            {
                ThirdPersonBody.SetAnimatorController(thirdPersonAnimatorController);
            }
        }

        public bool IsAnimatorStateName(string stateName)
        {
            if (FirstPersonBody != null)
            {
                return FirstPersonBody.Animator.GetCurrentAnimatorStateInfo(PlayerBodyController.EquipItemLayerIndex).IsName(stateName);
            }
            else if (ThirdPersonBody != null)
            {
                return ThirdPersonBody.Animator.GetCurrentAnimatorStateInfo(PlayerBodyController.EquipItemLayerIndex).IsName(stateName);
            }

            return false;
        }
        
        
        // PLAYER BODY ANIMATION
        
        public void SetTrigger(int param)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetTrigger(param);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetTrigger(param);
        }
        
        public void ResetTrigger(int param)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetBool(param, false);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetBool(param, false);
        }
        
        public void SetBool(int param, bool value)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetBool(param, value);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetBool(param, value);
        }
        
        public void SetInt(int param, int value)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetInteger(param, value);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetInteger(param, value);
        }
        
        public void SetFloat(int param, float value)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetFloat(param, value);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetFloat(param, value);
        }
        
        public void SetLayerWeight(int layerIndex, float value)
        {
            if (FirstPersonBody != null) FirstPersonBody.Animator.SetLayerWeight(layerIndex, value);
            if (ThirdPersonBody != null) ThirdPersonBody.Animator.SetLayerWeight(layerIndex, value);
        }
    }
}