// Copyright Isto Inc.

using Isto.Core.Installers;
using Isto.Core.Networking;
using Isto.Core.Photon;
using UnityEngine;

namespace Isto
{
    public class TRPProjectInstaller : CoreProjectInstaller
    {
        [Header("Networking")]
        public bool installNetworking = false;

        protected override void InstallNetworking()
        {
            if (installNetworking)
            {
                Container.Bind<INetworkManager>().To<PUN2NetworkManager>().FromComponentInHierarchy();
                Container.Bind<PUN2NetworkManager>().FromComponentInHierarchy();
                Container.Bind<INetworkEventsHandler>().To<PUN2NetworkMessager>().FromNew().AsSingle();
            }
            else
            {
                // Will inject dummy handlers.
                base.InstallNetworking();
            }
        }
    }
}
