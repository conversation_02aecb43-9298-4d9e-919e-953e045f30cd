// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.Cheats;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Photon;
using Isto.Core.UI;
using System.Runtime.Remoting.Messaging;
using UnityEngine;
using Zenject;

namespace Isto.Core.Installers
{
    public class TRPEssentialsInstaller : MonoInstaller<TRPEssentialsInstaller>
    {
        [Header("Game Data")]
        public bool enableDataManagers = true;

        [Header("Cheat Menu")]
        public CheatMenuState cheatMenuPrefab;


        // INJECTION

        CheatSettings _cheatSettings;

        [Inject]
        public void Inject(CheatSettings cheatSettings)
        {
            _cheatSettings = cheatSettings;
        }


        // OTHER METHODS

        public override void InstallBindings()
        {
            BindSettings();

            BindManagers();
            BindUI();
            BindDataManagers();
            BindCheaterUI();
        }

        private void BindSettings()
        {
            GameplaySettings settingsToUse = null;

            GameState gameState = Container.Resolve<GameState>();
            GameModeDefinition currentMode = gameState.CurrentGameMode;
            SceneSettingMappings settingsMapping = gameState.settingsMapping;

            settingsToUse = settingsMapping.defaultSettings;

            Container.Bind<GameplaySettings>().FromInstance(settingsToUse).AsSingle();
        }

        private void BindManagers()
        {
            Container.Bind<IPlayerFactory>().To<PUN2PlayerFactory>().FromComponentInHierarchy().AsSingle();
            Container.Bind<PlayerManager>().FromComponentInHierarchy().AsSingle();
        }

        private void BindUI()
        {
            // UI Menu States
            Container.Bind<UISimpleMenusClosedState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<UISimpleGameMenuState>().FromComponentInHierarchy().AsSingle();
            Container.Bind<SimpleGameMenuStateMachine>().FromComponentInHierarchy().AsSingle();
            Container.Bind<IUIGameMenu>().FromComponentInHierarchy().AsSingle();

            Container.Bind<UIAccessManager>().AsSingle();
        }

        private void BindDataManagers()
        {
            if (!enableDataManagers)
                return;

            // Need to parent under this item to make sure it's created in the right scene when loading
            Transform dataManagersParent = new GameObject("Data Managers").transform;
            dataManagersParent.SetParent(transform);

            Container.Bind<PlayerDataManager>().FromNewComponentOn(dataManagersParent.gameObject).AsSingle().NonLazy();
        }

        private void BindCheaterUI()
        {
            if (_cheatSettings.CheatsEnabled)
            {
                Container.Bind<CheatMenuState>().FromComponentsInNewPrefab(cheatMenuPrefab).AsSingle().NonLazy();
            }
        }
    }
}