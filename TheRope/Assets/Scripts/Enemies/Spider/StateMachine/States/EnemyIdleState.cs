// Copyright Isto Inc.


using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyIdleState : EnemyState
    {
        // UNITY HOOKUP



        // OTHER FIELDS

        private float _timer;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
            _debugStateMessage = "Spider is Idling...";
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _timer += Time.deltaTime;

            // Example transition: after idleDuration, go patrol
            if (_timer >= _spiderController.IdleDuration)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Patrol);
            }

            if (_spiderController.TryFindNearestPlayerWithinRadius())
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            return this;
        }
    }
}