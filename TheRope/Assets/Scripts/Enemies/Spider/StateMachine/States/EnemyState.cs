// Copyright Isto Inc.

using Isto.Core.StateMachine;

namespace Isto.TRP.Enemies
{
    public class EnemyState : MonoState
    {
        protected SpiderController _spiderController;
        protected string _debugStateMessage;
        public string DebugStateMessage => _debugStateMessage;

        public override void Enter(MonoStateMachine controller)
        {
            _spiderController = controller as SpiderController;
        }

        public override void Exit(MonoStateMachine controller)
        {
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return null;
        }
    }
}