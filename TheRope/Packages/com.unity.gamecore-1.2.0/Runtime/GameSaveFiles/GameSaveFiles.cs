using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using Unity.GameCore.Interop;

namespace Unity.GameCore
{
    public partial class SDK
    {
        public delegate void XGameSaveFilesGetFolderWithUiCompleted(Int32 hresult, string folderResult);

        public static void XGameSaveFilesGetFolderWithUiAsync(XUserHandle requestingUser, string configurationId, XGameSaveFilesGetFolderWithUiCompleted completionRoutine)
        {
            XAsyncBlockPtr asyncBlock = AsyncHelpers.WrapAsyncBlock(defaultQueue.handle, (block =>
            {
                SizeT folderSize = new SizeT(261); // MAX_PATH + /0
                Byte[] folderResultPtr = new Byte[261];
                int hrGetResult = XGRInterop.XGameSaveFilesGetFolderWithUiResult(block, folderSize, folderResultPtr);

                if (HR.FAILED(hrGetResult))
                    completionRoutine(hrGetResult, null);

                completionRoutine(hrGetResult, Converters.ByteArrayToString(folderResultPtr));
            }));

            Int32 hr = HR.S_OK;
            if (requestingUser != null)
                XGRInterop.XGameSaveFilesGetFolderWithUiAsync(requestingUser.InteropHandle, Converters.StringToNullTerminatedUTF8ByteArray(configurationId), asyncBlock);
            else
                XGRInterop.XGameSaveFilesGetFolderWithUiAsync(new Interop.XUserHandle(), Converters.StringToNullTerminatedUTF8ByteArray(configurationId), asyncBlock);

            if (!HR.FAILED(hr))
                return;

            AsyncHelpers.CleanupAsyncBlock(asyncBlock);
            completionRoutine(hr, null);
        }

        public static Int32 XGameSaveFilesGetRemainingQuota(XUserHandle userContext, string configurationId, out Int64 remainingQuota)
        {
            return XGRInterop.XGameSaveFilesGetRemainingQuota(userContext.InteropHandle, Converters.StringToNullTerminatedUTF8ByteArray(configurationId), out remainingQuota);
        }
    }
}
