using System;
using System.Runtime.InteropServices;

namespace Unity.GameCore.Interop
{
    partial class XGRInterop
    {
        //STDAPI XGameSaveFilesGetFolderWithUiAsync(_In_ XUserHandle requestingUser, _In_z_ const char* configurationId, _In_ XAsyncBlock* async) noexcept;
        [DllImport(ThunkDllName, CallingConvention = CallingConvention.StdCall)]
        public static extern Int32 XGameSaveFilesGetFolderWithUiAsync(XUserHandle requestingUser, Byte[] configurationId, XAsyncBlockPtr async);

        //STDAPI XGameSaveFilesGetFolderWithUiResult(_In_ XAsyncBlock* async, _In_ size_t folderSize, _Out_writes_bytes_(folderSize) char* folderResult) noexcept;
        [DllImport(ThunkDllName, CallingConvention = CallingConvention.StdCall)]
        public static extern Int32 XGameSaveFilesGetFolderWithUiResult(XAsyncBlockPtr async, SizeT folderSize, [Out] Byte[] folderResult);

        //STDAPI XGameSaveFilesGetRemainingQuota(_In_ XUserHandle userContext, _In_z_ const char* configurationId, _Out_ int64_t* remainingQuota) noexcept;
        [DllImport(ThunkDllName, CallingConvention = CallingConvention.StdCall)]
        public static extern Int32 XGameSaveFilesGetRemainingQuota(XUserHandle userContext, byte[] configurationId, out Int64 remainingQuota);
    }
}
