// Copyright Isto Inc.
using Isto.Atrio.Tests;
using System.Collections;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Isto.Core.Tests.PlayMode
{
    /// <summary>
    /// Helpers methods for common operations we perform in code
    /// </summary>
    public static class UnityTestUtils
    {
        public static GameObject FindTestObjectWithId(string id)
        {
            TestObjectIdentifier[] testObjects = GameObject.FindObjectsOfType<TestObjectIdentifier>(true);

            for (int i = 0; i < testObjects.Length; i++)
            {
                if (testObjects[i].id.Equals(id))
                    return testObjects[i].gameObject;
            }

            return null;
        }

        public static bool TryFindTestObjectUnderParentWithId(string id, Transform parent, out GameObject testObject)
        {
            TestObjectIdentifier[] testObjects = parent.GetComponentsInChildren<TestObjectIdentifier>(true);

            for (int i = 0; i < testObjects.Length; i++)
            {
                if (testObjects[i].id.Equals(id))
                {
                    testObject = testObjects[i].gameObject;
                    return true;
                }
            }

            testObject = null;
            return false;
        }

        /// <summary>
        /// Coroutine that unloads all the scenes by name
        /// </summary>
        /// <param name="sceneNames">Array of scene names, must be in the build settings</param>
        /// <returns></returns>
        public static IEnumerator UnloadMultipleScenes(string[] sceneNames)
        {
            UnityEngine.TestTools.LogAssert.ignoreFailingMessages = true;

            int finalSceneCount = SceneManager.sceneCount - sceneNames.Length;

            for (int i = 0; i < sceneNames.Length; i++)
            {
                Scene loadedScene = SceneManager.GetSceneByName(sceneNames[i]);

                if (loadedScene.IsValid())
                {
                    AsyncOperation sceneUnload = SceneManager.UnloadSceneAsync(sceneNames[i], UnloadSceneOptions.UnloadAllEmbeddedSceneObjects);

                    if (sceneUnload == null)
                    {
                        Debug.LogError("Cannot unload scene.");
                        yield break;
                    }

                    yield return sceneUnload;

                    while (!sceneUnload.isDone)
                        yield return null;
                }
                else
                {
                    Debug.Log("Loaded scene is not valid");
                }
            }

            yield return new WaitForSeconds(0.5f);

            Debug.Log($"End of Unload Call.  Scene Count:{SceneManager.sceneCount}.");

            if (SceneManager.sceneCount != finalSceneCount)
            {
                Debug.LogError("Too many scenes still loaded.  Waiting a bit longer");
                yield return new WaitForSeconds(2f);
            }

            UnityEngine.TestTools.LogAssert.ignoreFailingMessages = false;
        }
    }
}