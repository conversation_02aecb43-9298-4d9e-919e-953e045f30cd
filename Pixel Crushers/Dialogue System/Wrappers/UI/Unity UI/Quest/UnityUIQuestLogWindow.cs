// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Dialogue System/UI/Unity UI/Quest/Unity UI Quest Log Window")]
    public class UnityUIQuestLogWindow : PixelCrushers.DialogueSystem.UnityUIQuestLogWindow
    {
    }

}
