#if USE_ARTICY

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml.Serialization;

// 
// This source code was auto-generated by xsd, Version=4.6.1055.0.
// 
namespace PixelCrushers.DialogueSystem.Articy.Articy_3_1
{


    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    [System.Xml.Serialization.XmlRootAttribute("Export", Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd", IsNullable = false)]
    public partial class ExportType
    {

        private SettingsType settingsField;

        private ContentType contentField;

        private HierarchyType hierarchyField;

        private ExportErrorsType exportErrorsField;

        private string versionField;

        private string creatorToolField;

        private string creatorVersionField;

        private System.DateTime createdOnField;

        public ExportType()
        {
            this.versionField = "3.0";
        }

        /// <remarks/>
        public SettingsType Settings
        {
            get
            {
                return this.settingsField;
            }
            set
            {
                this.settingsField = value;
            }
        }

        /// <remarks/>
        public ContentType Content
        {
            get
            {
                return this.contentField;
            }
            set
            {
                this.contentField = value;
            }
        }

        /// <remarks/>
        public HierarchyType Hierarchy
        {
            get
            {
                return this.hierarchyField;
            }
            set
            {
                this.hierarchyField = value;
            }
        }

        /// <remarks/>
        public ExportErrorsType ExportErrors
        {
            get
            {
                return this.exportErrorsField;
            }
            set
            {
                this.exportErrorsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Version
        {
            get
            {
                return this.versionField;
            }
            set
            {
                this.versionField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CreatorTool
        {
            get
            {
                return this.creatorToolField;
            }
            set
            {
                this.creatorToolField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CreatorVersion
        {
            get
            {
                return this.creatorVersionField;
            }
            set
            {
                this.creatorVersionField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public System.DateTime CreatedOn
        {
            get
            {
                return this.createdOnField;
            }
            set
            {
                this.createdOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class SettingsType
    {

        private bool exportMarkupField;

        private bool exportQueriesField;

        private bool writeNamespaceField;

        private bool writeAllVariablesField;

        /// <remarks/>
        public bool ExportMarkup
        {
            get
            {
                return this.exportMarkupField;
            }
            set
            {
                this.exportMarkupField = value;
            }
        }

        /// <remarks/>
        public bool ExportQueries
        {
            get
            {
                return this.exportQueriesField;
            }
            set
            {
                this.exportQueriesField = value;
            }
        }

        /// <remarks/>
        public bool WriteNamespace
        {
            get
            {
                return this.writeNamespaceField;
            }
            set
            {
                this.writeNamespaceField = value;
            }
        }

        /// <remarks/>
        public bool WriteAllVariables
        {
            get
            {
                return this.writeAllVariablesField;
            }
            set
            {
                this.writeAllVariablesField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ErrorEntryType
    {

        private ErrorSeverityType severityField;

        private string valueField;

        public ErrorEntryType()
        {
            this.severityField = ErrorSeverityType.Soft;
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        //[PixelCrushers] [System.ComponentModel.DefaultValueAttribute(ErrorSeverityType.Soft)]
        public ErrorSeverityType Severity
        {
            get
            {
                return this.severityField;
            }
            set
            {
                this.severityField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum ErrorSeverityType
    {

        /// <remarks/>
        Soft,

        /// <remarks/>
        Hard,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ExportErrorsType
    {

        private ErrorEntryType[] itemsField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Error")]
        public ErrorEntryType[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class NodeType
    {

        private NodeType[] nodeField;

        private string[] textField;

        private string idRefField;

        private string typeField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Node")]
        public NodeType[] Node
        {
            get
            {
                return this.nodeField;
            }
            set
            {
                this.nodeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class HierarchyType
    {

        private NodeType nodeField;

        /// <remarks/>
        public NodeType Node
        {
            get
            {
                return this.nodeField;
            }
            set
            {
                this.nodeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ContentType
    {

        private object[] itemsField;

        private ItemsChoiceType[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Asset", typeof(AssetType))]
        [System.Xml.Serialization.XmlElementAttribute("Assets", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("AssetsUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("BooleanPropertyDefinition", typeof(BooleanPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("Comment", typeof(CommentType))]
        [System.Xml.Serialization.XmlElementAttribute("Condition", typeof(ConditionType))]
        [System.Xml.Serialization.XmlElementAttribute("Connection", typeof(ConnectionType))]
        [System.Xml.Serialization.XmlElementAttribute("Dialogue", typeof(DialogueType))]
        [System.Xml.Serialization.XmlElementAttribute("DialogueFragment", typeof(DialogueFragmentType))]
        [System.Xml.Serialization.XmlElementAttribute("Document", typeof(DocumentType))]
        [System.Xml.Serialization.XmlElementAttribute("Documents", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("DocumentsUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Entities", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("EntitiesUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Entity", typeof(EntityType))]
        [System.Xml.Serialization.XmlElementAttribute("EnumerationPropertyDefinition", typeof(EnumerationPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("FeatureDefinition", typeof(FeatureDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("Features", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("FeaturesUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Flow", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("FlowFragment", typeof(FlowFragmentType))]
        [System.Xml.Serialization.XmlElementAttribute("GlobalVariables", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Hub", typeof(HubType))]
        [System.Xml.Serialization.XmlElementAttribute("Instruction", typeof(InstructionType))]
        [System.Xml.Serialization.XmlElementAttribute("Journey", typeof(JourneyType))]
        [System.Xml.Serialization.XmlElementAttribute("Journeys", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("JourneysUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Jump", typeof(JumpType))]
        [System.Xml.Serialization.XmlElementAttribute("LayerFolder", typeof(LayerFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Link", typeof(LinkType))]
        [System.Xml.Serialization.XmlElementAttribute("Location", typeof(LocationType))]
        [System.Xml.Serialization.XmlElementAttribute("LocationImage", typeof(LocationImageType))]
        [System.Xml.Serialization.XmlElementAttribute("LocationText", typeof(LocationTextType))]
        [System.Xml.Serialization.XmlElementAttribute("Locations", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("LocationsUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("NumberPropertyDefinition", typeof(NumberPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectCustomization", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectTemplateDefinition", typeof(ObjectTemplateDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectTemplates", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectTemplatesUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("Path", typeof(PathType))]
        [System.Xml.Serialization.XmlElementAttribute("Project", typeof(ProjectType))]
        [System.Xml.Serialization.XmlElementAttribute("ProjectSettings", typeof(ProjectSettingsType))]
        [System.Xml.Serialization.XmlElementAttribute("PropertyTemplates", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("QueryReferenceStripPropertyDefinition", typeof(QueryReferenceStripPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("ReferenceSlotPropertyDefinition", typeof(ReferenceSlotPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("ReferenceStripPropertyDefinition", typeof(ReferenceStripPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("ScriptPropertyDefinition", typeof(ScriptPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("Spot", typeof(SpotType))]
        [System.Xml.Serialization.XmlElementAttribute("TextObject", typeof(TextObjectType))]
        [System.Xml.Serialization.XmlElementAttribute("TextPropertyDefinition", typeof(TextPropertyDefinitionType))]
        [System.Xml.Serialization.XmlElementAttribute("TypedObjectTemplates", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("TypedPropertyTemplates", typeof(SystemFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("TypedPropertyTemplatesUserFolder", typeof(UserFolderType))]
        [System.Xml.Serialization.XmlElementAttribute("VariableSet", typeof(VariableSetType))]
        [System.Xml.Serialization.XmlElementAttribute("Zone", typeof(ZoneType))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class AssetType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private PreviewImageType previewImageField;

        private string assetFilenameField;

        private string assetPathField;

        private string originalSourceField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public string AssetFilename
        {
            get
            {
                return this.assetFilenameField;
            }
            set
            {
                this.assetFilenameField = value;
            }
        }

        /// <remarks/>
        public string AssetPath
        {
            get
            {
                return this.assetPathField;
            }
            set
            {
                this.assetPathField = value;
            }
        }

        /// <remarks/>
        public string OriginalSource
        {
            get
            {
                return this.originalSourceField;
            }
            set
            {
                this.originalSourceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocalizableTextType
    {

        private LocalizedStringType[] localizedStringField;

        private int countField;

        private bool hasMarkupField;

        public LocalizableTextType()
        {
            this.hasMarkupField = false;
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LocalizedString")]
        public LocalizedStringType[] LocalizedString
        {
            get
            {
                return this.localizedStringField;
            }
            set
            {
                this.localizedStringField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        //[PixelCrushers] [System.ComponentModel.DefaultValueAttribute(false)]
        public bool HasMarkup
        {
            get
            {
                return this.hasMarkupField;
            }
            set
            {
                this.hasMarkupField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocalizedStringType
    {

        private string langField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string Lang
        {
            get
            {
                return this.langField;
            }
            set
            {
                this.langField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FeaturesType
    {

        private FeatureType[] featureField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Feature")]
        public FeatureType[] Feature
        {
            get
            {
                return this.featureField;
            }
            set
            {
                this.featureField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FeatureType
    {

        private PropertiesType[] propertiesField;

        private string nameField;

        private string idRefField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Properties")]
        public PropertiesType[] Properties
        {
            get
            {
                return this.propertiesField;
            }
            set
            {
                this.propertiesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PropertiesType
    {

        private object[] itemsField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Boolean", typeof(BooleanPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("Enum", typeof(EnumPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("LocalizableText", typeof(LocalizableTextPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("NamedReference", typeof(ReferenceSlotPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("Number", typeof(NumberPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("References", typeof(ReferenceStripPropertyType))]
        [System.Xml.Serialization.XmlElementAttribute("String", typeof(StringPropertyType))]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class BooleanPropertyType
    {

        private string nameField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class EnumPropertyType
    {

        private string nameField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocalizableTextPropertyType
    {

        private LocalizedStringType[] localizedStringField;

        private string nameField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LocalizedString")]
        public LocalizedStringType[] LocalizedString
        {
            get
            {
                return this.localizedStringField;
            }
            set
            {
                this.localizedStringField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferenceSlotPropertyType : NamedReferenceType
    {
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ReferenceSlotPropertyType))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class NamedReferenceType
    {

        private string nameField;

        private string idRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class NumberPropertyType
    {

        private string nameField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferenceStripPropertyType : ReferencesType
    {

        private string nameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ReferenceStripPropertyType))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferencesType
    {

        private ReferenceType[] referenceField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Reference")]
        public ReferenceType[] Reference
        {
            get
            {
                return this.referenceField;
            }
            set
            {
                this.referenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferenceType
    {

        private string idRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class StringPropertyType
    {

        private string nameField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PreviewImageType
    {

        private RectangleType viewBoxField;

        private string idRefField;

        private ViewBoxModeType modeField;

        /// <remarks/>
        public RectangleType ViewBox
        {
            get
            {
                return this.viewBoxField;
            }
            set
            {
                this.viewBoxField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ViewBoxModeType Mode
        {
            get
            {
                return this.modeField;
            }
            set
            {
                this.modeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class RectangleType
    {

        private float minXField;

        private float minYField;

        private float maxXField;

        private float maxYField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float MinX
        {
            get
            {
                return this.minXField;
            }
            set
            {
                this.minXField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float MinY
        {
            get
            {
                return this.minYField;
            }
            set
            {
                this.minYField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float MaxX
        {
            get
            {
                return this.maxXField;
            }
            set
            {
                this.maxXField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float MaxY
        {
            get
            {
                return this.maxYField;
            }
            set
            {
                this.maxYField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum ViewBoxModeType
    {

        /// <remarks/>
        FromAsset,

        /// <remarks/>
        Custom,

        /// <remarks/>
        Auto,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class SystemFolderType
    {

        private string displayNameField;

        private string colorField;

        private string idField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class UserFolderType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private string idField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class BooleanPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private int defaultValueField;

        private bool defaultValueFieldSpecified;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public int DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DefaultValueSpecified
        {
            get
            {
                return this.defaultValueFieldSpecified;
            }
            set
            {
                this.defaultValueFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class CommentType
    {

        private LocalizableTextType textField;

        private string colorField;

        private string urlField;

        private System.DateTime createdOnField;

        private string createdByField;

        private string idField;

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public System.DateTime CreatedOn
        {
            get
            {
                return this.createdOnField;
            }
            set
            {
                this.createdOnField = value;
            }
        }

        /// <remarks/>
        public string CreatedBy
        {
            get
            {
                return this.createdByField;
            }
            set
            {
                this.createdByField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ConditionType
    {

        private string displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private string expressionField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public string Expression
        {
            get
            {
                return this.expressionField;
            }
            set
            {
                this.expressionField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PinsType
    {

        private PinType[] pinField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Pin")]
        public PinType[] Pin
        {
            get
            {
                return this.pinField;
            }
            set
            {
                this.pinField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PinType
    {

        private string expressionField;

        private string idField;

        private int indexField;

        private SemanticType semanticField;

        /// <remarks/>
        public string Expression
        {
            get
            {
                return this.expressionField;
            }
            set
            {
                this.expressionField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Index
        {
            get
            {
                return this.indexField;
            }
            set
            {
                this.indexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public SemanticType Semantic
        {
            get
            {
                return this.semanticField;
            }
            set
            {
                this.semanticField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum SemanticType
    {

        /// <remarks/>
        Input,

        /// <remarks/>
        Output,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PointType
    {

        private float xField;

        private float yField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float X
        {
            get
            {
                return this.xField;
            }
            set
            {
                this.xField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Y
        {
            get
            {
                return this.yField;
            }
            set
            {
                this.yField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class SizeType
    {

        private float widthField;

        private float heightField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Width
        {
            get
            {
                return this.widthField;
            }
            set
            {
                this.widthField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Height
        {
            get
            {
                return this.heightField;
            }
            set
            {
                this.heightField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ConnectionType
    {

        private string colorField;

        private string technicalNameField;

        private string urlField;

        private LocalizableTextType labelField;

        private ConnectionRefType sourceField;

        private ConnectionRefType targetField;

        private bool showLabelField;

        private string idField;

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Label
        {
            get
            {
                return this.labelField;
            }
            set
            {
                this.labelField = value;
            }
        }

        /// <remarks/>
        public ConnectionRefType Source
        {
            get
            {
                return this.sourceField;
            }
            set
            {
                this.sourceField = value;
            }
        }

        /// <remarks/>
        public ConnectionRefType Target
        {
            get
            {
                return this.targetField;
            }
            set
            {
                this.targetField = value;
            }
        }

        /// <remarks/>
        public bool ShowLabel
        {
            get
            {
                return this.showLabelField;
            }
            set
            {
                this.showLabelField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ConnectionRefType
    {

        private string idRefField;

        private string pinRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string PinRef
        {
            get
            {
                return this.pinRefField;
            }
            set
            {
                this.pinRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class DialogueType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private ReferencesType referencesField;

        private PreviewImageType previewImageField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class DialogueFragmentType
    {

        private string displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private ReferenceType speakerField;

        private LocalizableTextType stageDirectionsField;

        private LocalizableTextType menuTextField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public ReferenceType Speaker
        {
            get
            {
                return this.speakerField;
            }
            set
            {
                this.speakerField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType StageDirections
        {
            get
            {
                return this.stageDirectionsField;
            }
            set
            {
                this.stageDirectionsField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType MenuText
        {
            get
            {
                return this.menuTextField;
            }
            set
            {
                this.menuTextField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class DocumentType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class EntityType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private ReferencesType referencesField;

        private PreviewImageType previewImageField;

        private string[] text1Field;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text1
        {
            get
            {
                return this.text1Field;
            }
            set
            {
                this.text1Field = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class EnumerationPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private int defaultValueField;

        private bool defaultValueFieldSpecified;

        private EnumerationValuesDefinitionType valuesField;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public int DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DefaultValueSpecified
        {
            get
            {
                return this.defaultValueFieldSpecified;
            }
            set
            {
                this.defaultValueFieldSpecified = value;
            }
        }

        /// <remarks/>
        public EnumerationValuesDefinitionType Values
        {
            get
            {
                return this.valuesField;
            }
            set
            {
                this.valuesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class EnumerationValuesDefinitionType
    {

        private EnumValueType[] enumValueField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("EnumValue")]
        public EnumValueType[] EnumValue
        {
            get
            {
                return this.enumValueField;
            }
            set
            {
                this.enumValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class EnumValueType
    {

        private int valueField;

        private string technicalNameField;

        private LocalizableTextType displayNameField;

        /// <remarks/>
        public int Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }

        /// <remarks/>
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FeatureDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string urlField;

        private PropertyDefinitionsType propertyDefinitionsField;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public PropertyDefinitionsType PropertyDefinitions
        {
            get
            {
                return this.propertyDefinitionsField;
            }
            set
            {
                this.propertyDefinitionsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PropertyDefinitionsType
    {

        private PropertyDefinitionRefType[] propertyDefinitionRefField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PropertyDefinitionRef")]
        public PropertyDefinitionRefType[] PropertyDefinitionRef
        {
            get
            {
                return this.propertyDefinitionRefField;
            }
            set
            {
                this.propertyDefinitionRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PropertyDefinitionRefType
    {

        private string idRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FlowFragmentType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private ReferencesType referencesField;

        private PreviewImageType previewImageField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class HubType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private string[] text1Field;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text1
        {
            get
            {
                return this.text1Field;
            }
            set
            {
                this.text1Field = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class InstructionType
    {

        private string displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private string expressionField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public string Expression
        {
            get
            {
                return this.expressionField;
            }
            set
            {
                this.expressionField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private ReferencesType referencesField;

        private JourneySettingsType settingsField;

        private VariableValuesListType initialVariableValuesField;

        private JourneyPointsType journeyPointsField;

        private string idField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public JourneySettingsType Settings
        {
            get
            {
                return this.settingsField;
            }
            set
            {
                this.settingsField = value;
            }
        }

        /// <remarks/>
        public VariableValuesListType InitialVariableValues
        {
            get
            {
                return this.initialVariableValuesField;
            }
            set
            {
                this.initialVariableValuesField = value;
            }
        }

        /// <remarks/>
        public JourneyPointsType JourneyPoints
        {
            get
            {
                return this.journeyPointsField;
            }
            set
            {
                this.journeyPointsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneySettingsType
    {

        private BackgroundImageModeType backgroundImageModeField;

        private ReferenceType backgroundImageField;

        private BackgroundImagePositioningModeType backgroundImagePositioningModeField;

        private BackgroundColorModeType backgroundColorModeField;

        private string backgroundColorField;

        private BackgroundColorGradientModeType backgroundColorGradientModeField;

        private int durationField;

        private TransitionModeType transitionModeField;

        /// <remarks/>
        public BackgroundImageModeType BackgroundImageMode
        {
            get
            {
                return this.backgroundImageModeField;
            }
            set
            {
                this.backgroundImageModeField = value;
            }
        }

        /// <remarks/>
        public ReferenceType BackgroundImage
        {
            get
            {
                return this.backgroundImageField;
            }
            set
            {
                this.backgroundImageField = value;
            }
        }

        /// <remarks/>
        public BackgroundImagePositioningModeType BackgroundImagePositioningMode
        {
            get
            {
                return this.backgroundImagePositioningModeField;
            }
            set
            {
                this.backgroundImagePositioningModeField = value;
            }
        }

        /// <remarks/>
        public BackgroundColorModeType BackgroundColorMode
        {
            get
            {
                return this.backgroundColorModeField;
            }
            set
            {
                this.backgroundColorModeField = value;
            }
        }

        /// <remarks/>
        public string BackgroundColor
        {
            get
            {
                return this.backgroundColorField;
            }
            set
            {
                this.backgroundColorField = value;
            }
        }

        /// <remarks/>
        public BackgroundColorGradientModeType BackgroundColorGradientMode
        {
            get
            {
                return this.backgroundColorGradientModeField;
            }
            set
            {
                this.backgroundColorGradientModeField = value;
            }
        }

        /// <remarks/>
        public int Duration
        {
            get
            {
                return this.durationField;
            }
            set
            {
                this.durationField = value;
            }
        }

        /// <remarks/>
        public TransitionModeType TransitionMode
        {
            get
            {
                return this.transitionModeField;
            }
            set
            {
                this.transitionModeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum BackgroundImageModeType
    {

        /// <remarks/>
        FromNode,

        /// <remarks/>
        Custom,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum BackgroundImagePositioningModeType
    {

        /// <remarks/>
        Fitting,

        /// <remarks/>
        Filling,

        /// <remarks/>
        Stretched,

        /// <remarks/>
        Repeating,

        /// <remarks/>
        Centered,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum BackgroundColorModeType
    {

        /// <remarks/>
        FromNode,

        /// <remarks/>
        Custom,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum BackgroundColorGradientModeType
    {

        /// <remarks/>
        Off,

        /// <remarks/>
        On,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum TransitionModeType
    {

        /// <remarks/>
        None,

        /// <remarks/>
        Animated,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class VariableValuesListType
    {

        private JourneyVariable[] variableField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Variable")]
        public JourneyVariable[] Variable
        {
            get
            {
                return this.variableField;
            }
            set
            {
                this.variableField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyVariable
    {

        private string nameField;

        private VariableDataTypeType dataTypeField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public VariableDataTypeType DataType
        {
            get
            {
                return this.dataTypeField;
            }
            set
            {
                this.dataTypeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum VariableDataTypeType
    {

        /// <remarks/>
        Boolean,

        /// <remarks/>
        Integer,

        /// <remarks/>
        String,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyPointsType
    {

        private JourneyPointType[] journeyPointField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("JourneyPoint")]
        public JourneyPointType[] JourneyPoint
        {
            get
            {
                return this.journeyPointField;
            }
            set
            {
                this.journeyPointField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyPointType
    {

        private JourneyRefType targetField;

        private LocalizableTextType textField;

        private string externalIdField;

        private string shortIdField;

        private JourneyPointSettingsType settingsField;

        private VariableValuesListType changedVariableValuesField;

        private JourneyMethodReturnValuesType methodReturnValuesField;

        private TypeOfJourneyPointType typeField;

        private ReachedByType reachedByField;

        /// <remarks/>
        public JourneyRefType Target
        {
            get
            {
                return this.targetField;
            }
            set
            {
                this.targetField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public JourneyPointSettingsType Settings
        {
            get
            {
                return this.settingsField;
            }
            set
            {
                this.settingsField = value;
            }
        }

        /// <remarks/>
        public VariableValuesListType ChangedVariableValues
        {
            get
            {
                return this.changedVariableValuesField;
            }
            set
            {
                this.changedVariableValuesField = value;
            }
        }

        /// <remarks/>
        public JourneyMethodReturnValuesType MethodReturnValues
        {
            get
            {
                return this.methodReturnValuesField;
            }
            set
            {
                this.methodReturnValuesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public TypeOfJourneyPointType Type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ReachedByType ReachedBy
        {
            get
            {
                return this.reachedByField;
            }
            set
            {
                this.reachedByField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyRefType
    {

        private string idRefField;

        private string pinRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string PinRef
        {
            get
            {
                return this.pinRefField;
            }
            set
            {
                this.pinRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyPointSettingsType
    {

        private BackgroundImageModeType backgroundImageModeField;

        private ReferenceType backgroundImageField;

        private BackgroundImagePositioningModeType backgroundImagePositioningModeField;

        private BackgroundColorModeType backgroundColorModeField;

        private string backgroundColorField;

        private BackgroundColorGradientModeType backgroundColorGradientModeField;

        private int durationField;

        /// <remarks/>
        public BackgroundImageModeType BackgroundImageMode
        {
            get
            {
                return this.backgroundImageModeField;
            }
            set
            {
                this.backgroundImageModeField = value;
            }
        }

        /// <remarks/>
        public ReferenceType BackgroundImage
        {
            get
            {
                return this.backgroundImageField;
            }
            set
            {
                this.backgroundImageField = value;
            }
        }

        /// <remarks/>
        public BackgroundImagePositioningModeType BackgroundImagePositioningMode
        {
            get
            {
                return this.backgroundImagePositioningModeField;
            }
            set
            {
                this.backgroundImagePositioningModeField = value;
            }
        }

        /// <remarks/>
        public BackgroundColorModeType BackgroundColorMode
        {
            get
            {
                return this.backgroundColorModeField;
            }
            set
            {
                this.backgroundColorModeField = value;
            }
        }

        /// <remarks/>
        public string BackgroundColor
        {
            get
            {
                return this.backgroundColorField;
            }
            set
            {
                this.backgroundColorField = value;
            }
        }

        /// <remarks/>
        public BackgroundColorGradientModeType BackgroundColorGradientMode
        {
            get
            {
                return this.backgroundColorGradientModeField;
            }
            set
            {
                this.backgroundColorGradientModeField = value;
            }
        }

        /// <remarks/>
        public int Duration
        {
            get
            {
                return this.durationField;
            }
            set
            {
                this.durationField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyMethodReturnValuesType
    {

        private string scriptTextField;

        private JourneyMethodReturnValueType[] methodValueField;

        private int countField;

        /// <remarks/>
        public string ScriptText
        {
            get
            {
                return this.scriptTextField;
            }
            set
            {
                this.scriptTextField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MethodValue")]
        public JourneyMethodReturnValueType[] MethodValue
        {
            get
            {
                return this.methodValueField;
            }
            set
            {
                this.methodValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JourneyMethodReturnValueType
    {

        private string nameField;

        private VariableDataTypeType dataTypeField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public VariableDataTypeType DataType
        {
            get
            {
                return this.dataTypeField;
            }
            set
            {
                this.dataTypeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum TypeOfJourneyPointType
    {

        /// <remarks/>
        FlowFragment,

        /// <remarks/>
        Dialogue,

        /// <remarks/>
        DialogueFragment,

        /// <remarks/>
        Connection,

        /// <remarks/>
        Pin,

        /// <remarks/>
        Hub,

        /// <remarks/>
        Jump,

        /// <remarks/>
        FlowConnectionSelection,

        /// <remarks/>
        DialogueConnectionSelection,

        /// <remarks/>
        InputPinSelection,

        /// <remarks/>
        OutputPinSelection,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum ReachedByType
    {

        /// <remarks/>
        Invalid,

        /// <remarks/>
        JourneyStart,

        /// <remarks/>
        Skip,

        /// <remarks/>
        Next,

        /// <remarks/>
        Submerge,

        /// <remarks/>
        Emerge,

        /// <remarks/>
        Branch,

        /// <remarks/>
        EndPoint,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class JumpType
    {

        private string displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private PinsType pinsField;

        private PointType positionField;

        private SizeType sizeField;

        private float zIndexField;

        private ConnectionRefType targetField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public PinsType Pins
        {
            get
            {
                return this.pinsField;
            }
            set
            {
                this.pinsField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public SizeType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public ConnectionRefType Target
        {
            get
            {
                return this.targetField;
            }
            set
            {
                this.targetField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LayerFolderType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private float zIndexField;

        private string idField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum VisibilityType
    {

        /// <remarks/>
        Invisible,

        /// <remarks/>
        Visible,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum SelectabilityType
    {

        /// <remarks/>
        Unselectable,

        /// <remarks/>
        Selectable,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LinkType
    {

        private string displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PointType positionField;

        private float zIndexField;

        private bool showDisplayNameField;

        private string displayNameColorField;

        private bool dropShadowField;

        private ReferenceType targetField;

        private LinkStyleType styleField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public bool ShowDisplayName
        {
            get
            {
                return this.showDisplayNameField;
            }
            set
            {
                this.showDisplayNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColor
        {
            get
            {
                return this.displayNameColorField;
            }
            set
            {
                this.displayNameColorField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public ReferenceType Target
        {
            get
            {
                return this.targetField;
            }
            set
            {
                this.targetField = value;
            }
        }

        /// <remarks/>
        public LinkStyleType Style
        {
            get
            {
                return this.styleField;
            }
            set
            {
                this.styleField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LinkStyleType
    {

        private LinkStyleKindType kindField;

        private SizeNamesType sizeField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public LinkStyleKindType Kind
        {
            get
            {
                return this.kindField;
            }
            set
            {
                this.kindField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public SizeNamesType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum LinkStyleKindType
    {

        /// <remarks/>
        Inherited,

        /// <remarks/>
        PreviewImage,

        /// <remarks/>
        IconOnly,

        /// <remarks/>
        Minimal,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum SizeNamesType
    {

        /// <remarks/>
        Inherited,

        /// <remarks/>
        Large,

        /// <remarks/>
        Medium,

        /// <remarks/>
        Small,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private ReferencesType referencesField;

        private PreviewImageType previewImageField;

        private LocationSettingsType settingsField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public LocationSettingsType Settings
        {
            get
            {
                return this.settingsField;
            }
            set
            {
                this.settingsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationSettingsType
    {

        private int showDisplayNameForZoneField;

        private int dropShadowForZoneField;

        private float displayNameSizeForZoneField;

        private string displayNameColorForZoneField;

        private int showDisplayNameForPathField;

        private int dropShadowForPathField;

        private float displayNameSizeForPathField;

        private string displayNameColorForPathField;

        private int showDisplayNameForImageField;

        private int dropShadowForImageField;

        private float displayNameSizeForImageField;

        private string displayNameColorForImageField;

        private int showDisplayNameForSpotField;

        private int dropShadowForSpotField;

        private string displayNameColorForSpotField;

        private SpotStyleKindType spotStyleKindField;

        private SizeNamesType spotStyleSizeField;

        private int showDisplayNameForLinkField;

        private int dropShadowForLinkField;

        private string displayNameColorForLinkField;

        private LinkStyleKindType linkStyleKindField;

        private SizeNamesType linkStyleSizeField;

        private int dropShadowForTextField;

        /// <remarks/>
        public int ShowDisplayNameForZone
        {
            get
            {
                return this.showDisplayNameForZoneField;
            }
            set
            {
                this.showDisplayNameForZoneField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForZone
        {
            get
            {
                return this.dropShadowForZoneField;
            }
            set
            {
                this.dropShadowForZoneField = value;
            }
        }

        /// <remarks/>
        public float DisplayNameSizeForZone
        {
            get
            {
                return this.displayNameSizeForZoneField;
            }
            set
            {
                this.displayNameSizeForZoneField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColorForZone
        {
            get
            {
                return this.displayNameColorForZoneField;
            }
            set
            {
                this.displayNameColorForZoneField = value;
            }
        }

        /// <remarks/>
        public int ShowDisplayNameForPath
        {
            get
            {
                return this.showDisplayNameForPathField;
            }
            set
            {
                this.showDisplayNameForPathField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForPath
        {
            get
            {
                return this.dropShadowForPathField;
            }
            set
            {
                this.dropShadowForPathField = value;
            }
        }

        /// <remarks/>
        public float DisplayNameSizeForPath
        {
            get
            {
                return this.displayNameSizeForPathField;
            }
            set
            {
                this.displayNameSizeForPathField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColorForPath
        {
            get
            {
                return this.displayNameColorForPathField;
            }
            set
            {
                this.displayNameColorForPathField = value;
            }
        }

        /// <remarks/>
        public int ShowDisplayNameForImage
        {
            get
            {
                return this.showDisplayNameForImageField;
            }
            set
            {
                this.showDisplayNameForImageField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForImage
        {
            get
            {
                return this.dropShadowForImageField;
            }
            set
            {
                this.dropShadowForImageField = value;
            }
        }

        /// <remarks/>
        public float DisplayNameSizeForImage
        {
            get
            {
                return this.displayNameSizeForImageField;
            }
            set
            {
                this.displayNameSizeForImageField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColorForImage
        {
            get
            {
                return this.displayNameColorForImageField;
            }
            set
            {
                this.displayNameColorForImageField = value;
            }
        }

        /// <remarks/>
        public int ShowDisplayNameForSpot
        {
            get
            {
                return this.showDisplayNameForSpotField;
            }
            set
            {
                this.showDisplayNameForSpotField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForSpot
        {
            get
            {
                return this.dropShadowForSpotField;
            }
            set
            {
                this.dropShadowForSpotField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColorForSpot
        {
            get
            {
                return this.displayNameColorForSpotField;
            }
            set
            {
                this.displayNameColorForSpotField = value;
            }
        }

        /// <remarks/>
        public SpotStyleKindType SpotStyleKind
        {
            get
            {
                return this.spotStyleKindField;
            }
            set
            {
                this.spotStyleKindField = value;
            }
        }

        /// <remarks/>
        public SizeNamesType SpotStyleSize
        {
            get
            {
                return this.spotStyleSizeField;
            }
            set
            {
                this.spotStyleSizeField = value;
            }
        }

        /// <remarks/>
        public int ShowDisplayNameForLink
        {
            get
            {
                return this.showDisplayNameForLinkField;
            }
            set
            {
                this.showDisplayNameForLinkField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForLink
        {
            get
            {
                return this.dropShadowForLinkField;
            }
            set
            {
                this.dropShadowForLinkField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColorForLink
        {
            get
            {
                return this.displayNameColorForLinkField;
            }
            set
            {
                this.displayNameColorForLinkField = value;
            }
        }

        /// <remarks/>
        public LinkStyleKindType LinkStyleKind
        {
            get
            {
                return this.linkStyleKindField;
            }
            set
            {
                this.linkStyleKindField = value;
            }
        }

        /// <remarks/>
        public SizeNamesType LinkStyleSize
        {
            get
            {
                return this.linkStyleSizeField;
            }
            set
            {
                this.linkStyleSizeField = value;
            }
        }

        /// <remarks/>
        public int DropShadowForText
        {
            get
            {
                return this.dropShadowForTextField;
            }
            set
            {
                this.dropShadowForTextField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum SpotStyleKindType
    {

        /// <remarks/>
        Inherited,

        /// <remarks/>
        Flag,

        /// <remarks/>
        Cross,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationImageType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PreviewImageType previewImageField;

        private PointType positionField;

        private float zIndexField;

        private FillType fillField;

        private OutlineType outlineField;

        private bool showDisplayNameField;

        private string displayNameColorField;

        private int displayNameSizeField;

        private bool dropShadowField;

        private PointType displayNamePositionField;

        private TransformationType transformationField;

        private VerticesType[] coordinatesField;

        private float opacityField;

        private ReferenceType imageField;

        private RectangleType clipRectField;

        private LocationAnchorsType anchorsField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        private CShapeType cShapeField;

        public LocationImageType()
        {
            this.cShapeField = CShapeType.Rectangle;
        }

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public FillType Fill
        {
            get
            {
                return this.fillField;
            }
            set
            {
                this.fillField = value;
            }
        }

        /// <remarks/>
        public OutlineType Outline
        {
            get
            {
                return this.outlineField;
            }
            set
            {
                this.outlineField = value;
            }
        }

        /// <remarks/>
        public bool ShowDisplayName
        {
            get
            {
                return this.showDisplayNameField;
            }
            set
            {
                this.showDisplayNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColor
        {
            get
            {
                return this.displayNameColorField;
            }
            set
            {
                this.displayNameColorField = value;
            }
        }

        /// <remarks/>
        public int DisplayNameSize
        {
            get
            {
                return this.displayNameSizeField;
            }
            set
            {
                this.displayNameSizeField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public PointType DisplayNamePosition
        {
            get
            {
                return this.displayNamePositionField;
            }
            set
            {
                this.displayNamePositionField = value;
            }
        }

        /// <remarks/>
        public TransformationType Transformation
        {
            get
            {
                return this.transformationField;
            }
            set
            {
                this.transformationField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Vertices", IsNullable = false)]
        public VerticesType[] Coordinates
        {
            get
            {
                return this.coordinatesField;
            }
            set
            {
                this.coordinatesField = value;
            }
        }

        /// <remarks/>
        public float Opacity
        {
            get
            {
                return this.opacityField;
            }
            set
            {
                this.opacityField = value;
            }
        }

        /// <remarks/>
        public ReferenceType Image
        {
            get
            {
                return this.imageField;
            }
            set
            {
                this.imageField = value;
            }
        }

        /// <remarks/>
        public RectangleType ClipRect
        {
            get
            {
                return this.clipRectField;
            }
            set
            {
                this.clipRectField = value;
            }
        }

        /// <remarks/>
        public LocationAnchorsType Anchors
        {
            get
            {
                return this.anchorsField;
            }
            set
            {
                this.anchorsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public CShapeType CShape
        {
            get
            {
                return this.cShapeField;
            }
            set
            {
                this.cShapeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FillType
    {

        private string colorField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class OutlineType
    {

        private string colorField;

        private int sizeField;

        private StrokeStyleType styleField;

        public OutlineType()
        {
            this.sizeField = 1;
            this.styleField = StrokeStyleType.Solid;
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        //[PixelCrushers] [System.ComponentModel.DefaultValueAttribute(1)]
        public int Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        //[PixelCrushers] [System.ComponentModel.DefaultValueAttribute(StrokeStyleType.Solid)]
        public StrokeStyleType Style
        {
            get
            {
                return this.styleField;
            }
            set
            {
                this.styleField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum StrokeStyleType
    {

        /// <remarks/>
        Solid,

        /// <remarks/>
        Dot,

        /// <remarks/>
        Dash,

        /// <remarks/>
        DashDot,

        /// <remarks/>
        DashDotDot,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class TransformationType
    {

        private float rotationField;

        private PointType pivotField;

        private PointType xAxisField;

        private PointType yAxisField;

        private PointType translationField;

        private string transformMatrixField;

        private RectangleType boundsField;

        /// <remarks/>
        public float Rotation
        {
            get
            {
                return this.rotationField;
            }
            set
            {
                this.rotationField = value;
            }
        }

        /// <remarks/>
        public PointType Pivot
        {
            get
            {
                return this.pivotField;
            }
            set
            {
                this.pivotField = value;
            }
        }

        /// <remarks/>
        public PointType XAxis
        {
            get
            {
                return this.xAxisField;
            }
            set
            {
                this.xAxisField = value;
            }
        }

        /// <remarks/>
        public PointType YAxis
        {
            get
            {
                return this.yAxisField;
            }
            set
            {
                this.yAxisField = value;
            }
        }

        /// <remarks/>
        public PointType Translation
        {
            get
            {
                return this.translationField;
            }
            set
            {
                this.translationField = value;
            }
        }

        /// <remarks/>
        public string TransformMatrix
        {
            get
            {
                return this.transformMatrixField;
            }
            set
            {
                this.transformMatrixField = value;
            }
        }

        /// <remarks/>
        public RectangleType Bounds
        {
            get
            {
                return this.boundsField;
            }
            set
            {
                this.boundsField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class VerticesType
    {

        private CoordinateReferenceType referenceField;

        private int countField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public CoordinateReferenceType Reference
        {
            get
            {
                return this.referenceField;
            }
            set
            {
                this.referenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum CoordinateReferenceType
    {

        /// <remarks/>
        Local,

        /// <remarks/>
        World,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationAnchorsType
    {

        private LocationAnchorType[] anchorField;

        private int countField;

        private bool countFieldSpecified;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Anchor")]
        public LocationAnchorType[] Anchor
        {
            get
            {
                return this.anchorField;
            }
            set
            {
                this.anchorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CountSpecified
        {
            get
            {
                return this.countFieldSpecified;
            }
            set
            {
                this.countFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationAnchorType
    {

        private float xField;

        private bool xFieldSpecified;

        private float yField;

        private bool yFieldSpecified;

        private string colorField;

        private AnchorSizeNamesType sizeField;

        private bool sizeFieldSpecified;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float X
        {
            get
            {
                return this.xField;
            }
            set
            {
                this.xField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool XSpecified
        {
            get
            {
                return this.xFieldSpecified;
            }
            set
            {
                this.xFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Y
        {
            get
            {
                return this.yField;
            }
            set
            {
                this.yField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool YSpecified
        {
            get
            {
                return this.yFieldSpecified;
            }
            set
            {
                this.yFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public AnchorSizeNamesType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SizeSpecified
        {
            get
            {
                return this.sizeFieldSpecified;
            }
            set
            {
                this.sizeFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum AnchorSizeNamesType
    {

        /// <remarks/>
        Small,

        /// <remarks/>
        Medium,

        /// <remarks/>
        Large,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum CShapeType
    {

        /// <remarks/>
        Polygon,

        /// <remarks/>
        Circle,

        /// <remarks/>
        Rectangle,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class LocationTextType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PreviewImageType previewImageField;

        private PointType positionField;

        private float zIndexField;

        private FillType fillField;

        private OutlineType outlineField;

        private bool dropShadowField;

        private PointType displayNamePositionField;

        private TransformationType transformationField;

        private VerticesType[] coordinatesField;

        private LocationAnchorsType anchorsField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        private CShapeType cShapeField;

        public LocationTextType()
        {
            this.cShapeField = CShapeType.Rectangle;
        }

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public FillType Fill
        {
            get
            {
                return this.fillField;
            }
            set
            {
                this.fillField = value;
            }
        }

        /// <remarks/>
        public OutlineType Outline
        {
            get
            {
                return this.outlineField;
            }
            set
            {
                this.outlineField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public PointType DisplayNamePosition
        {
            get
            {
                return this.displayNamePositionField;
            }
            set
            {
                this.displayNamePositionField = value;
            }
        }

        /// <remarks/>
        public TransformationType Transformation
        {
            get
            {
                return this.transformationField;
            }
            set
            {
                this.transformationField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Vertices", IsNullable = false)]
        public VerticesType[] Coordinates
        {
            get
            {
                return this.coordinatesField;
            }
            set
            {
                this.coordinatesField = value;
            }
        }

        /// <remarks/>
        public LocationAnchorsType Anchors
        {
            get
            {
                return this.anchorsField;
            }
            set
            {
                this.anchorsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public CShapeType CShape
        {
            get
            {
                return this.cShapeField;
            }
            set
            {
                this.cShapeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class NumberPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private decimal defaultValueField;

        private bool defaultValueFieldSpecified;

        private decimal minValueField;

        private bool minValueFieldSpecified;

        private decimal maxValueField;

        private bool maxValueFieldSpecified;

        private int precisionField;

        private bool precisionFieldSpecified;

        private string unitField;

        private int displayThousandsSeparatorField;

        private bool displayThousandsSeparatorFieldSpecified;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public decimal DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DefaultValueSpecified
        {
            get
            {
                return this.defaultValueFieldSpecified;
            }
            set
            {
                this.defaultValueFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal MinValue
        {
            get
            {
                return this.minValueField;
            }
            set
            {
                this.minValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MinValueSpecified
        {
            get
            {
                return this.minValueFieldSpecified;
            }
            set
            {
                this.minValueFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal MaxValue
        {
            get
            {
                return this.maxValueField;
            }
            set
            {
                this.maxValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxValueSpecified
        {
            get
            {
                return this.maxValueFieldSpecified;
            }
            set
            {
                this.maxValueFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int Precision
        {
            get
            {
                return this.precisionField;
            }
            set
            {
                this.precisionField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PrecisionSpecified
        {
            get
            {
                return this.precisionFieldSpecified;
            }
            set
            {
                this.precisionFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string Unit
        {
            get
            {
                return this.unitField;
            }
            set
            {
                this.unitField = value;
            }
        }

        /// <remarks/>
        public int DisplayThousandsSeparator
        {
            get
            {
                return this.displayThousandsSeparatorField;
            }
            set
            {
                this.displayThousandsSeparatorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DisplayThousandsSeparatorSpecified
        {
            get
            {
                return this.displayThousandsSeparatorFieldSpecified;
            }
            set
            {
                this.displayThousandsSeparatorFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ObjectTemplateDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeatureDefinitionsType featureDefinitionsField;

        private string idField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeatureDefinitionsType FeatureDefinitions
        {
            get
            {
                return this.featureDefinitionsField;
            }
            set
            {
                this.featureDefinitionsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FeatureDefinitionsType
    {

        private FeatureDefinitionRefType[] featureDefinitionRefField;

        private int countField;

        private bool countFieldSpecified;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FeatureDefinitionRef")]
        public FeatureDefinitionRefType[] FeatureDefinitionRef
        {
            get
            {
                return this.featureDefinitionRefField;
            }
            set
            {
                this.featureDefinitionRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CountSpecified
        {
            get
            {
                return this.countFieldSpecified;
            }
            set
            {
                this.countFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FeatureDefinitionRefType
    {

        private string idRefField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class PathType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PreviewImageType previewImageField;

        private PointType positionField;

        private float zIndexField;

        private FillType fillField;

        private OutlineType outlineField;

        private bool showDisplayNameField;

        private string displayNameColorField;

        private int displayNameSizeField;

        private bool dropShadowField;

        private PointType displayNamePositionField;

        private TransformationType transformationField;

        private VerticesType[] coordinatesField;

        private PathCapNamesType startCapField;

        private PathCapNamesType endCapField;

        private int lineWidthField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public FillType Fill
        {
            get
            {
                return this.fillField;
            }
            set
            {
                this.fillField = value;
            }
        }

        /// <remarks/>
        public OutlineType Outline
        {
            get
            {
                return this.outlineField;
            }
            set
            {
                this.outlineField = value;
            }
        }

        /// <remarks/>
        public bool ShowDisplayName
        {
            get
            {
                return this.showDisplayNameField;
            }
            set
            {
                this.showDisplayNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColor
        {
            get
            {
                return this.displayNameColorField;
            }
            set
            {
                this.displayNameColorField = value;
            }
        }

        /// <remarks/>
        public int DisplayNameSize
        {
            get
            {
                return this.displayNameSizeField;
            }
            set
            {
                this.displayNameSizeField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public PointType DisplayNamePosition
        {
            get
            {
                return this.displayNamePositionField;
            }
            set
            {
                this.displayNamePositionField = value;
            }
        }

        /// <remarks/>
        public TransformationType Transformation
        {
            get
            {
                return this.transformationField;
            }
            set
            {
                this.transformationField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Vertices", IsNullable = false)]
        public VerticesType[] Coordinates
        {
            get
            {
                return this.coordinatesField;
            }
            set
            {
                this.coordinatesField = value;
            }
        }

        /// <remarks/>
        public PathCapNamesType StartCap
        {
            get
            {
                return this.startCapField;
            }
            set
            {
                this.startCapField = value;
            }
        }

        /// <remarks/>
        public PathCapNamesType EndCap
        {
            get
            {
                return this.endCapField;
            }
            set
            {
                this.endCapField = value;
            }
        }

        /// <remarks/>
        public int LineWidth
        {
            get
            {
                return this.lineWidthField;
            }
            set
            {
                this.lineWidthField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum PathCapNamesType
    {

        /// <remarks/>
        ColoredDot,

        /// <remarks/>
        None,

        /// <remarks/>
        LineArrowHead,

        /// <remarks/>
        FilledArrowHead,

        /// <remarks/>
        Diamond,

        /// <remarks/>
        Square,

        /// <remarks/>
        Disc,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ProjectType
    {

        private string displayNameField;

        private string technicalNameField;

        private string urlField;

        private string idField;

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ProjectSettingsType
    {

        private FlowSettingsType flowSettingsField;

        private JourneySettingsType journeySettingsField;

        private LocationSettingsType locationSettingsField;

        private ExternalApplicationsType externalApplicationsField;

        private string idField;

        /// <remarks/>
        public FlowSettingsType FlowSettings
        {
            get
            {
                return this.flowSettingsField;
            }
            set
            {
                this.flowSettingsField = value;
            }
        }

        /// <remarks/>
        public JourneySettingsType JourneySettings
        {
            get
            {
                return this.journeySettingsField;
            }
            set
            {
                this.journeySettingsField = value;
            }
        }

        /// <remarks/>
        public LocationSettingsType LocationSettings
        {
            get
            {
                return this.locationSettingsField;
            }
            set
            {
                this.locationSettingsField = value;
            }
        }

        /// <remarks/>
        public ExternalApplicationsType ExternalApplications
        {
            get
            {
                return this.externalApplicationsField;
            }
            set
            {
                this.externalApplicationsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class FlowSettingsType
    {

        private int builtInScriptSupportField;

        private int gridSizeField;

        private int gridSizeEnforcedField;

        private int spacingHorizontalField;

        private int spacingVerticalField;

        private int spacingEnforcedField;

        /// <remarks/>
        public int BuiltInScriptSupport
        {
            get
            {
                return this.builtInScriptSupportField;
            }
            set
            {
                this.builtInScriptSupportField = value;
            }
        }

        /// <remarks/>
        public int GridSize
        {
            get
            {
                return this.gridSizeField;
            }
            set
            {
                this.gridSizeField = value;
            }
        }

        /// <remarks/>
        public int GridSizeEnforced
        {
            get
            {
                return this.gridSizeEnforcedField;
            }
            set
            {
                this.gridSizeEnforcedField = value;
            }
        }

        /// <remarks/>
        public int SpacingHorizontal
        {
            get
            {
                return this.spacingHorizontalField;
            }
            set
            {
                this.spacingHorizontalField = value;
            }
        }

        /// <remarks/>
        public int SpacingVertical
        {
            get
            {
                return this.spacingVerticalField;
            }
            set
            {
                this.spacingVerticalField = value;
            }
        }

        /// <remarks/>
        public int SpacingEnforced
        {
            get
            {
                return this.spacingEnforcedField;
            }
            set
            {
                this.spacingEnforcedField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ExternalApplicationsType
    {

        private ApplicationDefinitionType[] itemsField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ApplicationDefinition")]
        public ApplicationDefinitionType[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ApplicationDefinitionType
    {

        private string nameField;

        private string commandField;

        private string workingDirectoryField;

        /// <remarks/>
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        public string Command
        {
            get
            {
                return this.commandField;
            }
            set
            {
                this.commandField = value;
            }
        }

        /// <remarks/>
        public string WorkingDirectory
        {
            get
            {
                return this.workingDirectoryField;
            }
            set
            {
                this.workingDirectoryField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class QueryReferenceStripPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private string defaultValueField;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public string DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferenceSlotPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private ReferenceType defaultValueField;

        private ObjectTypes objectTypesField;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public ReferenceType DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        public ObjectTypes ObjectTypes
        {
            get
            {
                return this.objectTypesField;
            }
            set
            {
                this.objectTypesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ObjectTypes
    {

        private ObjectType[] itemsField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ObjectType")]
        public ObjectType[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ObjectType
    {

        private object[] itemsField;

        private int countField;

        private string typeField;

        private int allowUnsetTemplateField;

        private int allowAllTemplatesField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AllowedCategory", typeof(AllowedCategory))]
        [System.Xml.Serialization.XmlElementAttribute("AllowedTemplate", typeof(AllowedTemplate))]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int AllowUnsetTemplate
        {
            get
            {
                return this.allowUnsetTemplateField;
            }
            set
            {
                this.allowUnsetTemplateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int AllowAllTemplates
        {
            get
            {
                return this.allowAllTemplatesField;
            }
            set
            {
                this.allowAllTemplatesField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class AllowedCategory
    {

        private AssetCategoryType nameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public AssetCategoryType Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum AssetCategoryType
    {

        /// <remarks/>
        Image,

        /// <remarks/>
        Audio,

        /// <remarks/>
        Video,

        /// <remarks/>
        Document,

        /// <remarks/>
        Misc,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class AllowedTemplate
    {

        private string nameField;

        private string idRefField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IdRef
        {
            get
            {
                return this.idRefField;
            }
            set
            {
                this.idRefField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ReferenceStripPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private int maxReferenceCountField;

        private bool maxReferenceCountFieldSpecified;

        private ObjectTypes objectTypesField;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public int MaxReferenceCount
        {
            get
            {
                return this.maxReferenceCountField;
            }
            set
            {
                this.maxReferenceCountField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxReferenceCountSpecified
        {
            get
            {
                return this.maxReferenceCountFieldSpecified;
            }
            set
            {
                this.maxReferenceCountFieldSpecified = value;
            }
        }

        /// <remarks/>
        public ObjectTypes ObjectTypes
        {
            get
            {
                return this.objectTypesField;
            }
            set
            {
                this.objectTypesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ScriptPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private string defaultValueField;

        private ScriptTypeType scriptTypeField;

        private bool scriptTypeFieldSpecified;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public string DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        public ScriptTypeType ScriptType
        {
            get
            {
                return this.scriptTypeField;
            }
            set
            {
                this.scriptTypeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ScriptTypeSpecified
        {
            get
            {
                return this.scriptTypeFieldSpecified;
            }
            set
            {
                this.scriptTypeFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public enum ScriptTypeType
    {

        /// <remarks/>
        Unknown,

        /// <remarks/>
        Condition,

        /// <remarks/>
        Outcome,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class SpotType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PreviewImageType previewImageField;

        private PointType positionField;

        private float zIndexField;

        private bool showDisplayNameField;

        private string displayNameColorField;

        private bool dropShadowField;

        private SpotStyleType styleField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public bool ShowDisplayName
        {
            get
            {
                return this.showDisplayNameField;
            }
            set
            {
                this.showDisplayNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColor
        {
            get
            {
                return this.displayNameColorField;
            }
            set
            {
                this.displayNameColorField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public SpotStyleType Style
        {
            get
            {
                return this.styleField;
            }
            set
            {
                this.styleField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class SpotStyleType
    {

        private SpotStyleKindType kindField;

        private SizeNamesType sizeField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public SpotStyleKindType Kind
        {
            get
            {
                return this.kindField;
            }
            set
            {
                this.kindField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public SizeNamesType Size
        {
            get
            {
                return this.sizeField;
            }
            set
            {
                this.sizeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class TextObjectType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private ReferencesType referencesField;

        private PreviewImageType previewImageField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public ReferencesType References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class TextPropertyDefinitionType
    {

        private LocalizableTextType displayNameField;

        private string colorField;

        private string technicalNameField;

        private string tooltipTextField;

        private int isMandatoryField;

        private bool isMandatoryFieldSpecified;

        private int isLocalizedField;

        private bool isLocalizedFieldSpecified;

        private string placeholderValueField;

        private TextPropertyDefinitionValueType defaultValueField;

        private string disallowedCharsField;

        private int maxLengthField;

        private bool maxLengthFieldSpecified;

        private int allowsLinebreaksField;

        private bool allowsLinebreaksFieldSpecified;

        private string idField;

        private string basedOnField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string TooltipText
        {
            get
            {
                return this.tooltipTextField;
            }
            set
            {
                this.tooltipTextField = value;
            }
        }

        /// <remarks/>
        public int IsMandatory
        {
            get
            {
                return this.isMandatoryField;
            }
            set
            {
                this.isMandatoryField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsMandatorySpecified
        {
            get
            {
                return this.isMandatoryFieldSpecified;
            }
            set
            {
                this.isMandatoryFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int IsLocalized
        {
            get
            {
                return this.isLocalizedField;
            }
            set
            {
                this.isLocalizedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsLocalizedSpecified
        {
            get
            {
                return this.isLocalizedFieldSpecified;
            }
            set
            {
                this.isLocalizedFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string PlaceholderValue
        {
            get
            {
                return this.placeholderValueField;
            }
            set
            {
                this.placeholderValueField = value;
            }
        }

        /// <remarks/>
        public TextPropertyDefinitionValueType DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }

        /// <remarks/>
        public string DisallowedChars
        {
            get
            {
                return this.disallowedCharsField;
            }
            set
            {
                this.disallowedCharsField = value;
            }
        }

        /// <remarks/>
        public int MaxLength
        {
            get
            {
                return this.maxLengthField;
            }
            set
            {
                this.maxLengthField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MaxLengthSpecified
        {
            get
            {
                return this.maxLengthFieldSpecified;
            }
            set
            {
                this.maxLengthFieldSpecified = value;
            }
        }

        /// <remarks/>
        public int AllowsLinebreaks
        {
            get
            {
                return this.allowsLinebreaksField;
            }
            set
            {
                this.allowsLinebreaksField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AllowsLinebreaksSpecified
        {
            get
            {
                return this.allowsLinebreaksFieldSpecified;
            }
            set
            {
                this.allowsLinebreaksFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string BasedOn
        {
            get
            {
                return this.basedOnField;
            }
            set
            {
                this.basedOnField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class TextPropertyDefinitionValueType
    {

        private LocalizedStringType[] localizedStringField;

        private string[] textField;

        private int countField;

        private bool countFieldSpecified;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LocalizedString")]
        public LocalizedStringType[] LocalizedString
        {
            get
            {
                return this.localizedStringField;
            }
            set
            {
                this.localizedStringField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CountSpecified
        {
            get
            {
                return this.countFieldSpecified;
            }
            set
            {
                this.countFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class VariableSetType
    {

        private string urlField;

        private string technicalNameField;

        private LocalizableTextType descriptionField;

        private VariablesType variablesField;

        private string idField;

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }

        /// <remarks/>
        public VariablesType Variables
        {
            get
            {
                return this.variablesField;
            }
            set
            {
                this.variablesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class VariablesType
    {

        private VariableType[] variableField;

        private int countField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Variable")]
        public VariableType[] Variable
        {
            get
            {
                return this.variableField;
            }
            set
            {
                this.variableField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int Count
        {
            get
            {
                return this.countField;
            }
            set
            {
                this.countField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class VariableType
    {

        private string technicalNameField;

        private LocalizableTextType descriptionField;

        private VariableDataTypeType dataTypeField;

        private string defaultValueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }

        /// <remarks/>
        public VariableDataTypeType DataType
        {
            get
            {
                return this.dataTypeField;
            }
            set
            {
                this.dataTypeField = value;
            }
        }

        /// <remarks/>
        public string DefaultValue
        {
            get
            {
                return this.defaultValueField;
            }
            set
            {
                this.defaultValueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class ZoneType
    {

        private LocalizableTextType displayNameField;

        private LocalizableTextType textField;

        private string colorField;

        private string technicalNameField;

        private string externalIdField;

        private string shortIdField;

        private string urlField;

        private FeaturesType featuresField;

        private VisibilityType visibilityField;

        private SelectabilityType selectabilityField;

        private PreviewImageType previewImageField;

        private PointType positionField;

        private float zIndexField;

        private FillType fillField;

        private OutlineType outlineField;

        private bool showDisplayNameField;

        private string displayNameColorField;

        private int displayNameSizeField;

        private bool dropShadowField;

        private PointType displayNamePositionField;

        private TransformationType transformationField;

        private object itemField;

        private string idField;

        private string objectTemplateReferenceField;

        private string objectTemplateReferenceNameField;

        private CShapeType cShapeField;

        /// <remarks/>
        public LocalizableTextType DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }

        /// <remarks/>
        public LocalizableTextType Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        public string Color
        {
            get
            {
                return this.colorField;
            }
            set
            {
                this.colorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string TechnicalName
        {
            get
            {
                return this.technicalNameField;
            }
            set
            {
                this.technicalNameField = value;
            }
        }

        /// <remarks/>
        public string ExternalId
        {
            get
            {
                return this.externalIdField;
            }
            set
            {
                this.externalIdField = value;
            }
        }

        /// <remarks/>
        public string ShortId
        {
            get
            {
                return this.shortIdField;
            }
            set
            {
                this.shortIdField = value;
            }
        }

        /// <remarks/>
        public string Url
        {
            get
            {
                return this.urlField;
            }
            set
            {
                this.urlField = value;
            }
        }

        /// <remarks/>
        public FeaturesType Features
        {
            get
            {
                return this.featuresField;
            }
            set
            {
                this.featuresField = value;
            }
        }

        /// <remarks/>
        public VisibilityType Visibility
        {
            get
            {
                return this.visibilityField;
            }
            set
            {
                this.visibilityField = value;
            }
        }

        /// <remarks/>
        public SelectabilityType Selectability
        {
            get
            {
                return this.selectabilityField;
            }
            set
            {
                this.selectabilityField = value;
            }
        }

        /// <remarks/>
        public PreviewImageType PreviewImage
        {
            get
            {
                return this.previewImageField;
            }
            set
            {
                this.previewImageField = value;
            }
        }

        /// <remarks/>
        public PointType Position
        {
            get
            {
                return this.positionField;
            }
            set
            {
                this.positionField = value;
            }
        }

        /// <remarks/>
        public float ZIndex
        {
            get
            {
                return this.zIndexField;
            }
            set
            {
                this.zIndexField = value;
            }
        }

        /// <remarks/>
        public FillType Fill
        {
            get
            {
                return this.fillField;
            }
            set
            {
                this.fillField = value;
            }
        }

        /// <remarks/>
        public OutlineType Outline
        {
            get
            {
                return this.outlineField;
            }
            set
            {
                this.outlineField = value;
            }
        }

        /// <remarks/>
        public bool ShowDisplayName
        {
            get
            {
                return this.showDisplayNameField;
            }
            set
            {
                this.showDisplayNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayNameColor
        {
            get
            {
                return this.displayNameColorField;
            }
            set
            {
                this.displayNameColorField = value;
            }
        }

        /// <remarks/>
        public int DisplayNameSize
        {
            get
            {
                return this.displayNameSizeField;
            }
            set
            {
                this.displayNameSizeField = value;
            }
        }

        /// <remarks/>
        public bool DropShadow
        {
            get
            {
                return this.dropShadowField;
            }
            set
            {
                this.dropShadowField = value;
            }
        }

        /// <remarks/>
        public PointType DisplayNamePosition
        {
            get
            {
                return this.displayNamePositionField;
            }
            set
            {
                this.displayNamePositionField = value;
            }
        }

        /// <remarks/>
        public TransformationType Transformation
        {
            get
            {
                return this.transformationField;
            }
            set
            {
                this.transformationField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Circle", typeof(CircleType))]
        [System.Xml.Serialization.XmlElementAttribute("Coordinates", typeof(CoordinatesType))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReference
        {
            get
            {
                return this.objectTemplateReferenceField;
            }
            set
            {
                this.objectTemplateReferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ObjectTemplateReferenceName
        {
            get
            {
                return this.objectTemplateReferenceNameField;
            }
            set
            {
                this.objectTemplateReferenceNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public CShapeType CShape
        {
            get
            {
                return this.cShapeField;
            }
            set
            {
                this.cShapeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class CircleType
    {

        private float centerXField;

        private float centerYField;

        private float radiusField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float CenterX
        {
            get
            {
                return this.centerXField;
            }
            set
            {
                this.centerXField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float CenterY
        {
            get
            {
                return this.centerYField;
            }
            set
            {
                this.centerYField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Radius
        {
            get
            {
                return this.radiusField;
            }
            set
            {
                this.radiusField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    //[PixelCrushers] [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd")]
    public partial class CoordinatesType
    {

        private VerticesType[] verticesField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Vertices")]
        public VerticesType[] Vertices
        {
            get
            {
                return this.verticesField;
            }
            set
            {
                this.verticesField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.nevigo.com/schemas/articydraft/3.1/XmlContentExport_FullProject.xsd", IncludeInSchema = false)]
    public enum ItemsChoiceType
    {

        /// <remarks/>
        Asset,

        /// <remarks/>
        Assets,

        /// <remarks/>
        AssetsUserFolder,

        /// <remarks/>
        BooleanPropertyDefinition,

        /// <remarks/>
        Comment,

        /// <remarks/>
        Condition,

        /// <remarks/>
        Connection,

        /// <remarks/>
        Dialogue,

        /// <remarks/>
        DialogueFragment,

        /// <remarks/>
        Document,

        /// <remarks/>
        Documents,

        /// <remarks/>
        DocumentsUserFolder,

        /// <remarks/>
        Entities,

        /// <remarks/>
        EntitiesUserFolder,

        /// <remarks/>
        Entity,

        /// <remarks/>
        EnumerationPropertyDefinition,

        /// <remarks/>
        FeatureDefinition,

        /// <remarks/>
        Features,

        /// <remarks/>
        FeaturesUserFolder,

        /// <remarks/>
        Flow,

        /// <remarks/>
        FlowFragment,

        /// <remarks/>
        GlobalVariables,

        /// <remarks/>
        Hub,

        /// <remarks/>
        Instruction,

        /// <remarks/>
        Journey,

        /// <remarks/>
        Journeys,

        /// <remarks/>
        JourneysUserFolder,

        /// <remarks/>
        Jump,

        /// <remarks/>
        LayerFolder,

        /// <remarks/>
        Link,

        /// <remarks/>
        Location,

        /// <remarks/>
        LocationImage,

        /// <remarks/>
        LocationText,

        /// <remarks/>
        Locations,

        /// <remarks/>
        LocationsUserFolder,

        /// <remarks/>
        NumberPropertyDefinition,

        /// <remarks/>
        ObjectCustomization,

        /// <remarks/>
        ObjectTemplateDefinition,

        /// <remarks/>
        ObjectTemplates,

        /// <remarks/>
        ObjectTemplatesUserFolder,

        /// <remarks/>
        Path,

        /// <remarks/>
        Project,

        /// <remarks/>
        ProjectSettings,

        /// <remarks/>
        PropertyTemplates,

        /// <remarks/>
        QueryReferenceStripPropertyDefinition,

        /// <remarks/>
        ReferenceSlotPropertyDefinition,

        /// <remarks/>
        ReferenceStripPropertyDefinition,

        /// <remarks/>
        ScriptPropertyDefinition,

        /// <remarks/>
        Spot,

        /// <remarks/>
        TextObject,

        /// <remarks/>
        TextPropertyDefinition,

        /// <remarks/>
        TypedObjectTemplates,

        /// <remarks/>
        TypedPropertyTemplates,

        /// <remarks/>
        TypedPropertyTemplatesUserFolder,

        /// <remarks/>
        VariableSet,

        /// <remarks/>
        Zone,
    }
}
#endif
