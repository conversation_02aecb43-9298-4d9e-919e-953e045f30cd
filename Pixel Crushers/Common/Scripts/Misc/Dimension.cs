// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers
{

    /// <summary>
    /// This enum is used to specify 2D or 3D.
    /// </summary>
    public enum Dimension
    {
        /// <summary>
        /// The context is 2D (e.g., sprites, 2D Physics, etc.).
        /// </summary>
		Is2D,

        /// <summary>
        /// The context is 3D (e.g., models, 3D Physics, etc.).
        /// </summary>
		Is3D
    }

}
