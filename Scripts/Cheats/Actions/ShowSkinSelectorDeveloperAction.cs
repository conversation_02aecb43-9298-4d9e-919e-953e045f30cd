// Copyright Isto Inc.

using Isto.Core.UI;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Show Skin Selector Action", menuName = "Scriptables/Dev Mode Actions/Show Skin Selector Action")]
    public class ShowSkinSelectorDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            var console = GameObject.FindObjectOfType<CheatMenuState>();
            console.ShowSkinSelector();
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}