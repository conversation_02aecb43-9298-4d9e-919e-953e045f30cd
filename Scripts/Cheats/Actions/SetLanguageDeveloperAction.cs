// Copyright Isto Inc.

using Isto.Core.Installers;
using Isto.Core.Localization;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New Set Language Action", menuName = "Scriptables/Dev Mode Actions/Set Language Action")]
    public class SetLanguageDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            string langName = args[0].ToString();
            var langEnumValue = LanguageChanger.LanguageEnum.GetFromName(langName, System.StringComparison.OrdinalIgnoreCase) as LanguageChanger.LanguageEnum;
            LanguageChanger.SetLanguage(langEnumValue);
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }

        public override bool TryAutoCompleteParameters(string[] inputs, out string output)
        {
            LanguageList supportedLanguages = ProjectContext.Instance.Container.ResolveId<LanguageList>(InjectId.SUPPORTED_LANGUAGES);
            List<string> languageNames = supportedLanguages.GetLanguageNames();
            return TryAutoCompleteFromList(inputs, languageNames, out output);
        }
    }
}