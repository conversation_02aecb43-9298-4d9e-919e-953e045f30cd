// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Beings;
using Isto.Core.Items;
using Isto.Core.Power;
using Isto.Core.UI;
using System;
using UnityEngine;

namespace Isto.Core
{
    // Refactoring needed!

    // This is a file full of scope level class definitions, which is probably not the best as a start.

    // Also, we don't really need these declarations even if we keep using the events that currently refer to them -
    // we should be able to use a Tuple or something like that for those.
    // Note that EventArgs is only required if you use System.EventHandler, you can use any other delegate marked
    // "event" and it will also work as an event, but with whatever signature you want for your custom delegate.

    // Our new 2023 Code Style directives officially discourage the use of EventHandler and EventArgs.

    // For now note that this file also exists as one half of our EventArgs definitions, namely those that have
    // references in scope of the core module as of the time of this writing, as opposed to the AtrioCustomEventArgs
    // file that we have for references in the scope of the Atrio project (which are also deprecated but we're not
    // going to spend time refactoring away).

    // We might want to move some of these EventArgs back into AtrioCustomEventArgs if we realize we don't need them
    // in Core and if they are still used in Atrio.


    /// <summary> Deprecated </summary>
    public class ItemEventArgs : EventArgs
    {
        public CoreItem item;
        public int count;
        public IInventory container;
        public ItemPlacementController placement;

        public ItemEventArgs(IInventory container, CoreItem item, int count = 1, ItemPlacementController placement = null)
        {
            this.item = item;
            this.count = count;
            this.container = container;
            this.placement = placement;
        }
    }

    /// <summary> Deprecated </summary>
    public class AutomationItemEventArgs : EventArgs
    {
        public AutomationCoreItem automationItem;
        public CoreItem coreItem;

        public AutomationItemEventArgs(AutomationCoreItem item, CoreItem coreItem)
        {
            this.automationItem = item;
            this.coreItem = coreItem;
        }
    }

    /// <summary> Deprecated </summary>
    public class MenuInteractionEventArgs : EventArgs
    {
        public enum Action
        {
            Open,
            Close
        }

        public GameMenusEnum menu;
        public Action action;

        public MenuInteractionEventArgs(GameMenusEnum gameMenu, Action actionType)
        {
            menu = gameMenu;
            action = actionType;
        }
    }



    // Not sure if this particular one needs to be in Core
    // - PlayMusicAction listens to it and is likely to end up in the FMOD module
    // - Its only event is in AtrioGlobalGameplayEvents for now but maybe it should be moved to GlobalGameplayEvents
    /// <summary> Deprecated </summary>
    public class StoryEndedEventArgs : EventArgs
    {
        public string conversationID;
        public StoryEndedEventArgs(string conversationID)
        {
            this.conversationID = conversationID;
        }
    }

    /// <summary> Deprecated </summary>
    public class TetherEventArgs : EventArgs
    {
        public PowerTethered tethered;

        public TetherEventArgs(PowerTethered tethered)
        {
            this.tethered = tethered;
        }
    }

    /// <summary> Deprecated </summary>
    public class HealthEventArgs : EventArgs
    {
        public float damage;
        public Health.DamageTypeEnum type;
        public Health.DamageSourceEnum source;
        public float stunTime;
        public Transform sourceTransform;

        public HealthEventArgs(float damage, Health.DamageTypeEnum type, Health.DamageSourceEnum source, Transform damageSourceTransform = null, float stunTime = 0f)
        {
            this.damage = damage;
            this.type = type;
            this.stunTime = stunTime;
            this.source = source;
            this.sourceTransform = damageSourceTransform;
        }
    }

    /// <summary> Deprecated </summary>
    public class BuffEventArgs : EventArgs
    {
        public BuffEffect buffEffect;

        public BuffEventArgs(BuffEffect buffEffect)
        {
            this.buffEffect = buffEffect;
        }
    }

    /// <summary> Deprecated </summary>
    public class AutomationResourceEventArgs : EventArgs
    {
        public enum EventType { FullyHarvested, Deleted, FullyGrown }

        public EventType eventType;

        public AutomationResourceEventArgs(EventType type)
        {
            eventType = type;
        }
    }

    /// <summary> Deprecated </summary>
    public class AutomationPullEventArgs : EventArgs
    {
        public Vector3 directionOfPull;

        public AutomationPullEventArgs(Vector3 pull)
        {
            directionOfPull = pull;
        }
    }

    /// <summary> Deprecated </summary>
    public class NotificationEventArgs : EventArgs
    {
        public int countChange;
        public UINotification notification;

        public NotificationEventArgs(UINotification sender, int countChange)
        {
            this.notification = sender;
            this.countChange = countChange;
        }
    }
}