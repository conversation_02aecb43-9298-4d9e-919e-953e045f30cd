// Copyright Isto Inc.

using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    public class PlayerManager : MonoBehaviour
    {
        // OTHER FIELDS

        private CorePlayerController _localPlayerInstance;
        private List<CorePlayerController> _remotePlayerInstances;
        private List<CorePlayerController> _allPlayerInstances;


        // PROPERTIES

        /// <summary>
        /// For a single player game this is the only player you need to worry about.
        /// </summary>
        public virtual CorePlayerController Player
        {
            get
            {
                if (_localPlayerInstance == null)
                {
                    CorePlayerController player = CreatePlayer().GetComponent<CorePlayerController>();
                    if (player != null)
                    {
                        RegisterLocalPlayer(player);
                    }
                }
                return _localPlayerInstance;
            }
        }

        /// <summary>
        /// For a networked game this is the player you own.
        /// </summary>
        public virtual CorePlayerController LocalPlayer => _localPlayerInstance;

        /// <summary>
        /// For a networked game this is all connected players except you.
        /// </summary>
        public virtual List<CorePlayerController> RemotePlayers => _remotePlayerInstances;

        /// <summary>
        /// Gives you access to all known player instances.
        /// </summary>
        public virtual List<CorePlayerController> Players => _allPlayerInstances;

        public virtual int TotalPlayerCount => _allPlayerInstances.Count;


        // INJECTION

        private IPlayerFactory _playerFactory;

        [Inject]
        public void Inject(IPlayerFactory playerFactory)
        {
            _playerFactory = playerFactory;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _remotePlayerInstances = new List<CorePlayerController>();
            _allPlayerInstances = new List<CorePlayerController>();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        private void Start()
        {
            // We allow the player to be part of the essentials scene for most games.
            // If it is there, detect it and use it instead of creating a new instance.
            CorePlayerController player = GameObject.FindAnyObjectByType<CorePlayerController>();
            if (player != null)
            {
                RegisterLocalPlayer(player);
            }

            RegisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.Subscribe(Events.NETWORK_ROOM_JOINED, Events_OnNetworkRoomJoined);
            Events.SubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.NETWORK_ROOM_JOINED, Events_OnNetworkRoomJoined);
            Events.UnSubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }

        // I am assuming that in general we'll want the player's avatar to be spawned when he joins the room
        // but I suspect it should be done by a game script and not like this in core...
        // TODO: move this probably
        private void Events_OnNetworkRoomJoined()
        {
            var p = Player;
        }

        // TODO: we'd like to have an event to only receive messages when a player spawns
        private void Events_OnGameObjectSpawned(object[] args)
        {
            GameObject spawnedObj = (GameObject)args[0];
            CorePlayerController playerComponent = spawnedObj.GetComponent<CorePlayerController>();
            if (playerComponent != null)
            {
                OnPlayerSpawned(playerComponent);
            }
        }

        protected virtual void OnPlayerSpawned(CorePlayerController playerComponent)
        {
            if (_localPlayerInstance != playerComponent)
            {
                _remotePlayerInstances.Add(playerComponent);
                _allPlayerInstances.Add(playerComponent);
            }
        }


        // OTHER METHODS

        public virtual GameObject CreatePlayer()
        {
            GameObject player = _playerFactory.CreatePlayer();

            return player;
        }

        private void RegisterLocalPlayer(CorePlayerController playerInstance)
        {
            _localPlayerInstance = playerInstance;
            _allPlayerInstances.Add(playerInstance);
        }
    }
}