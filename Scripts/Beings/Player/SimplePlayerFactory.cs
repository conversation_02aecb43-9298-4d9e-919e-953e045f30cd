// Copyright Isto Inc.

using Isto.Core.Networking;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// A simple enough version of the player factory that should cover the basic needs of most small projects.
    /// It supports finding Respawn points based on tag, and it Spawns the player if the game is networked.
    /// </summary>
    public class SimplePlayerFactory : MonoBehaviour, IPlayerFactory
    {
        // Note: not sure yet if I want this to be a monobehaviour but I need either to hook it up in a prefab or a
        // scriptable object so I can get the prefab to instantiate... though it could be in the settings instead?
        [SerializeField]
        private CorePlayerController _playerPrefab;

        // INJECTION

        private INetworkManager _networkManager;
        private DiContainer _container;

        [Inject]
        public void Inject(INetworkManager networkManager,
                           DiContainer container)
        {
            _networkManager = networkManager;
            _container = container;
        }

        public GameObject CreatePlayer()
        {
            GameObject player = null;
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;

            // This is one of the default unity tags so might as well use it
            // TODO: move this magic string to a constant
            GameObject respawn = GameObject.FindGameObjectWithTag("Respawn");
            if (respawn != null)
            {
                spawnPosition = respawn.transform.position;
                spawnRotation = respawn.transform.rotation;
            }

            player = CreatePlayer(spawnPosition, spawnRotation);

            return player;
        }

        public GameObject CreatePlayer(Vector3 position, Quaternion rotation)
        {
            GameObject player = null;

            if (!_networkManager.IsMultiplayerAvailable())
            {
                player = Instantiate(_playerPrefab.gameObject, position, rotation);
            }
            else
            {
                player = _networkManager.Spawn(_playerPrefab.gameObject, position, rotation);
            }

            _container.InjectGameObject(player);

            return player;
        }
    }
}
