// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Automation;
using Isto.Core.Inputs;
using Isto.Core.Items;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    [CreateAssetMenu(fileName = "New Player Item Interaction State", menuName = "Scriptables/State/Player Item Interaction State")]
    public class PlayerInteractWithItemState : State
    {
        protected enum InteractionType
        {
            PickUpItem,
            DropItemFromInventory,
            GiveItem,
            MoveToThenInteract
        }


        // UNITY HOOKUP

        public float moveSpeed = 15f;
        [Tooltip("Distance from item before it is picked up. Some interactions are collider based however")]
        [SerializeField] private float _interactionRange = 1f;
        [Tooltip("Delay between interacting with item again if in range.")]
        public float exitDelay = 0.25f;

        [EventRef]
        public string pickupSoundRef = @"event:/SFX/Atrio SFX_Pick up-St";


        // OTHER FIELDS

        protected ItemPile _activeItem;
        protected Vector3 _interactionPosition;
        protected GameObject _interactionObject;

        protected InteractionType _currentActionType;
        protected IItemInteractionCallback _callback;
        private float _nextValidTime;
        private Collider[] _hitBuffer = new Collider[5];

        private float _minStateTime = 0.15f;                 // The minimun amount of time that can be spent in this state, gives input time to clear
        private float _stateTimer;                          // Timer to track how long this state has been active this time.
        private bool _interactionComplete;
        private State _nextState;
        private float _currentInteractionDistance;


        // PROPERTIES

        public GameObject InteractionObject { get { return _interactionObject; } }
        public float InteractionRange { get { return _currentInteractionDistance; } }


        // INJECTION

        protected AutomationPlayerController _controller;
        protected AutomationPlayerMoveState _moveState;
        protected PlayerInventory _inventory;
        protected IGameSounds _sounds;
        protected IControls _controls;
        protected AutomationSystem _autoSystem;
        protected IUIGameMenu _mainMenu;

        [Inject]
        public virtual void Inject(AutomationPlayerController playerController, IControls controls,
            AutomationPlayerMoveState playerMoveState, PlayerInventory playerInventory, IGameSounds sounds,
            AutomationSystem automationSystem, IUIGameMenu mainMenu)
        {
            _controller = playerController;
            _moveState = playerMoveState;
            _inventory = playerInventory;
            _sounds = sounds;
            _controls = controls;
            _autoSystem = automationSystem;
            _mainMenu = mainMenu;

            _currentInteractionDistance = _interactionRange;
        }


        // LIFECYCLE EVENTS

        public override void Enter(ScriptableStateMachine controller)
        {
#if PLAYER_LOGGING
            Debug.Log("Entering Interact with item state");
#endif
            _controller = controller as AutomationPlayerController;

            // Only set new movement target if needed
            if (_currentActionType != InteractionType.DropItemFromInventory)
            {
                if (!IsTargetInInteractionRange())
                {
                    SetMovementTowardsItem();
                }
            }

            if (IsTargetInInteractionRange() && _currentActionType == InteractionType.PickUpItem)
            {
                _nextState = InteractWithItem();
                if (_controls.UsingJoystick())
                {
                    _controller.ProcessMovementAndInteractions(true, false, false);
                }
            }
            else
            {
                _interactionComplete = false;
            }

            _nextValidTime = Time.time;
            _stateTimer = 0f;
        }

        public override void Exit(ScriptableStateMachine controller)
        {
#if PLAYER_LOGGING
            Debug.Log("Exiting Interact with item state");
#endif

            isInterruptable = true;
        }

        public override IState Run(ScriptableStateMachine controller)
        {
            _stateTimer += Time.deltaTime;

            if (_stateTimer > _minStateTime || _interactionComplete)
            {
                // If not in state long enough, don't check for new interactions, only abilities
                bool checkInteractions = _stateTimer > _minStateTime;

                State interruptState = _controller.ProcessMovementAndInteractions(true, true, checkInteractions);

                // If any new movement has been set, exit out of this state to the move state.  Interrupt check needs to be the first conditional to ensure it gets called if the others fail
                if (interruptState != null && _stateTimer > _minStateTime && !_interactionComplete)
                {
                    return interruptState;
                }
            }

            if (Time.time >= _nextValidTime)
            {
                if (_interactionComplete)
                {
                    isInterruptable = true;
                    return _nextState;
                }
                // Interaction not complete, check if in range
                else
                {
                    if (IsTargetInInteractionRange())
                    {
                        ResetTimers();
                        _nextState = InteractWithItem();
                    }
                    // Keep moving towards item
                    else
                    {
                        return this;
                    }
                }
            }

            // Waiting for interaction time to be valid or keep moving towards item
            return this;
        }


        // OTHER METHODS

        public void SetItemDrop(Vector3 position, ItemPile items, IItemInteractionCallback callback)
        {
            _currentActionType = InteractionType.DropItemFromInventory;

            _interactionPosition = position;
            _activeItem = items;
            _callback = callback;
        }

        public void SetPickupItem(IInteractable targetInteractable)
        {
            UnityUtils.TryGetGameObjectFromInterface(targetInteractable as Component, out GameObject targetGO);

            _interactionObject = targetGO;

            float direction = UnityUtils.GetRelativeHorizontalDirection(_controller.transform.position, targetGO.transform.position);

            _interactionPosition = targetInteractable.GetInteractionPosition(_controller.transform.position);
            _currentInteractionDistance = _interactionRange;

            _currentActionType = InteractionType.PickUpItem;
            _interactionComplete = false;
        }

        public void SetGiveItem(ItemPile items, GameObject targetObject, IItemInteractionCallback callback)
        {
            _currentActionType = InteractionType.GiveItem;
            _callback = callback;

            _interactionObject = targetObject;

            // Set interaction position as the closest point on a collider on the target to the player
            _interactionPosition = UnityUtils.GetNearestPositionOnCollider(targetObject, _controller.transform.position, _controller.pickupLayers);
            _currentInteractionDistance = _interactionRange;

            _activeItem = items;
        }

        public void SetInteractWithItem(IInteractable target, float interactionDistanceOverride = 0f)
        {
            _currentInteractionDistance = interactionDistanceOverride == 0f ? _interactionRange : interactionDistanceOverride;

            _currentActionType = InteractionType.MoveToThenInteract;

            UnityUtils.TryGetGameObjectFromInterface(target as Component, out GameObject targetGO);
            _interactionObject = targetGO;

            _interactionPosition = target.GetInteractionPosition(_controller.transform.position);
        }

        private void SetMovementTowardsItem()
        {
            // Set move to interaction position.
            _controller.MoveCharacterToPosition(_interactionPosition, moveSpeed);
        }

        /// <summary>
        /// Chooses the correct action based on the interaction type member variable.  Returns the next
        /// state that the controller should be in.
        /// </summary>
        /// <returns></returns>
        private State InteractWithItem()
        {
            _interactionComplete = true;
            isInterruptable = false;

            switch (_currentActionType)
            {
                case InteractionType.PickUpItem:
                    _controller.StopCharacter();
                    return PickupItem();
                case InteractionType.DropItemFromInventory:
                    DropItem();
                    break;
                case InteractionType.GiveItem:
                    return GiveItem();
                case InteractionType.MoveToThenInteract:
                    _controller.StopCharacter();
                    // Crafting can be opened without interrupting other movement - if we end up at the target while the menu is open, abort
                    if (_mainMenu.CurrentMenu != GameMenusEnum.CRAFTING)
                    {
                        _interactionObject.GetComponent<IInteractable>().PlayerInteraction(_controller, UserActions.INTERACT);
                        return _moveState;
                    }
                    break;
                default:
                    break;
            }

            return _moveState;
        }

        protected virtual State PickupItem()
        {
            // If the interaction object has been destroyed for some reason, go back to the move state.
            if (_interactionObject == null)
                return _moveState;

            int objectLayer = _interactionObject.layer;

            //TODO
            // I'd prefer if we'd offer the object to be interacted with and it would know how to react
            // I also wonder if we couldn't just make everything compatible with HandlePickupItem() instead of having to do different
            // for this SetDraggedMob thing that's probably just an old hack from the early atrio trailer too...

            if (objectLayer.Equals(Layers.COLLECTABLE) || objectLayer.Equals(Layers.ITEM_ON_GROUND) || objectLayer.Equals(Layers.PROJECTILE))
            {
                InteractableItem controller = _interactionObject.GetComponent<InteractableItem>();
                HandlePickupItem(controller);
            }
            else if (objectLayer.Equals(Layers.MOB))
            {
                if (_interactionObject.CompareTag(Tags.STUNNED_MOB))
                {
                    /*
                    _controller.dragState.SetDraggedMob(_interactionObject);
                    return _controller.dragState;*/
                }
                else if (_interactionObject.CompareTag(Tags.PLAYER_MOB))
                {
                    InteractableItem controller = _interactionObject.GetComponent<InteractableItem>();
                    HandlePickupItem(controller);
                }
            }

            return _moveState;
        }

        private void DropItem()
        {
            // Drop at player position
            Vector3 dropPosition = _controller.transform.position;
            dropPosition.y = 0f;

            // First check that there is nothing blocking dropping the item at the target position.
            int hitCount = Physics.OverlapSphereNonAlloc(dropPosition, 0.2f, _hitBuffer, Layers.BUILDING_MASK | Layers.COLLECTABLE_MASK);

            if (hitCount == 0)
            {
                int dropCount = _activeItem.item.dropSingle ? 1 : _activeItem.count;

                if (_activeItem.item is AdvancedItem advItem)
                {
                    _activeItem.item.DropItem(dropPosition, null, dropCount, true);
                }
                else
                {
                    AutomationCoreItem droppedItem = _autoSystem.CoreItemFactory.Create(new CoreItemParams(_activeItem.item.itemID, dropCount, dropPosition));

                    if (_activeItem.item.coreItemAssetOverride.RuntimeKeyIsValid())
                        droppedItem.SetDisplayAsset(_activeItem.item.coreItemAssetOverride);

                    _autoSystem.TryAddCoreItemToSpace(dropPosition, droppedItem);
                }

                if (_callback != null)
                {
                    _callback.Success(_activeItem, dropCount);
                }

                _activeItem = null;
            }
        }

        protected virtual State GiveItem()
        {
            _currentActionType = InteractionType.GiveItem;

            ItemContainer container = _interactionObject.GetComponent<ItemContainer>();

            if (container != null)
            {
                int successDeposit = container.Add(_activeItem);

                if (_callback != null)
                {
                    _callback.Success(_activeItem, successDeposit);
                }

                // Tell player to stop trying to move to position as we gave the item
                _controller.SetCharacterIdle();
            }

            return _moveState;
        }

        /// <summary>
        /// Resets the timers for the state, should be called after interacting with an item to prevent
        /// exiting the state too quickly
        /// </summary>
        private void ResetTimers()
        {
            _nextValidTime = Time.time + exitDelay;
            _stateTimer = _minStateTime - exitDelay;
        }

        /// <summary>
        /// Checks if the target object is within the interaction range set in this state.  Takes into account the NavMeshAgent
        /// collisions avoidance radius.
        /// </summary>
        /// <returns></returns>
        private bool IsTargetInInteractionRange()
        {
            switch (_currentActionType)
            {
                case InteractionType.PickUpItem:
                case InteractionType.DropItemFromInventory:
                case InteractionType.GiveItem:
                case InteractionType.MoveToThenInteract:
                    return Vector3.Distance(_controller.transform.position, _interactionPosition) < _currentInteractionDistance;
                default:
                    throw new UnityException("Trying to perform Interaction type that is not expected.");
            }
        }

        public bool HandlePickupItem(InteractableItem itemToPickUp)
        {
            bool success = false;
            int deposited = _inventory.Add(itemToPickUp.itemPile);
            itemToPickUp.ReduceCount(deposited);

            //If completely deposited, destroy the item
            if (itemToPickUp.itemPile.count == 0)
            {
                _sounds.PlayOneShot(AutomationGameSoundEnum.ITEM_PICKUP, _controller.transform.position);

                itemToPickUp.gameObject.CompletePickupActions();

                _sounds.PlayOneShot(pickupSoundRef, itemToPickUp.transform.position);

                // Clear internal reference
                _interactionObject = null;
                success = true;
            }

            return success;
        }
    }
}