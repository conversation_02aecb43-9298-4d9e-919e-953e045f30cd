// Copyright Isto Inc.
using Isto.Core.Automation;
using Isto.Core.Inputs;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    public class PlayerItemInteraction : MonoBehaviour, IItemInteractionCallback
    {
        [Serializable]
        public class InteractibleMetaData
        {
            public string name;
            public GameObject gameObject;
            public IInteractable interactable;
            public float distance;
            public int priority;

            public void Set(GameObject gameObject, IInteractable interactableScript, float distance)
            {
                this.gameObject = gameObject;
                name = gameObject.name;
                interactable = interactableScript;
                this.distance = distance;
                this.priority = GetPriority(gameObject);
            }
        }

        // Public Variables

        [Tooltip("Search range to look for item when pickup button is pressed.  Will move towards item.")]
        public float searchRange = 4f; // this is set to 1f in the prefab
        [Tooltip("Adjusts the position of the search in the forward direction of the player this distance.  Causes items in front of player to be closer than ones behind.  ONLY WITH CONTROLLER")]
        public float forwardSearchBias = 1f;
        [Tooltip("How big of a circle around the mouse will be considered under the mouse.")]
        public float mouseSearchRadius = 1f; // this is set to 0.2f in the prefab
        // Keeping the following fields and their old values around for reference during upcoming QA phase in case I need to reintroduce them
        //[Tooltip("The minimum distance between two objects to use item priority list when checking which to consider closest.  If over this value only distance is considered, not priority")]
        //public float minDistanceForPriorityOverride = 0.25f; // this is set to 2f in the prefab
        //[Tooltip("The nearest distance is set this much closer if the item is a higher priority. Allows higher priority items to be selected if they're slightly farther away than lower ones")]
        //public float adjustmentDistanceForHigherPriorityItems = 0.2f;

        public GameObject NearestInteractableGameObject { get { UnityUtils.TryGetGameObjectFromInterface(_selectedInteractable as Component, out GameObject nearestGO); return nearestGO; } }

        // The nearest interactable item to the player if using controller or under the mouse if using keyboard
        // Careful, null checks on interfaces for components don't really work because it circumvents unity's overrides.
        public IInteractable NearestInteractable { get { return _selectedInteractable; } }

        public GameObject ActiveInteractableGameObject { get { UnityUtils.TryGetGameObjectFromInterface(ActiveInteractable as Component, out GameObject activeGO); return activeGO; } }

        // The currently set interactable item.  Used to hold interactable while player is moving to position, in case mouse moves off
        // Careful, null checks on interfaces for components don't really work because it circumvents unity's overrides.
        public IInteractable ActiveInteractable { get; private set; }

        public GameObject ActiveObject { get; private set; }

        public EventHandler<ItemEventArgs> EquipppedItemUsed;

        public int CurrentEquippedItemSlot { get; set; }

        // Private Variables

        // These are the interactables that share an automation space with our point of interest, in order of priority
        [SerializeField] private List<InteractibleMetaData> _interactablesAtSelectionPosition = new List<InteractibleMetaData>();

#if UNITY_EDITOR
        [SerializeField] private bool _showInteractionPositionGizmo = false;
#endif

        private int bufferSize = 16;
        private float _spamTimer;
        private Collider[] _hitBuffer;
        private RaycastHit[] _raycastHitBuffer;
        private IInteractable _selectedInteractable;
        private List<UserActions> _actionList;
        private bool _showToolTips = true;
        private bool _allowInteractions = true;
        private LayerMask _interactionLayers; //Interaction layers are set in the player controller and loaded here on Awake()
        private bool _interactionOccured;

        // Injected
        private IControls _controls;
        private AutomationPlayerController _player;
        private PlayerInventory _inventory;
        private Transform _selectionHighlight;
        private AutomationSystem _automationSystem;
        // Lifecycle Events

        [Inject]
        public void Inject(IControls controls, AutomationPlayerController playerController, PlayerInventory inventory,
            [InjectOptional(Id = "SelectionHighlight")] Transform selectionHighlight, AutomationSystem automationSystem)
        {
            _controls = controls;
            _player = playerController;
            _inventory = inventory;
            _selectionHighlight = selectionHighlight;
            _automationSystem = automationSystem;
        }

        private void Awake()
        {
            _hitBuffer = new Collider[bufferSize];
            _raycastHitBuffer = new RaycastHit[bufferSize];
            _interactionLayers = _player.pickupLayers;

            _selectionHighlight?.gameObject.SetActive(false);
        }

        private void OnEnable()
        {
            Events.Subscribe(Events.PLAYER_INVENTORY_CHANGED_EVENT, OnPlayerInventoryChanged);
        }

        private void OnDisable()
        {
            Events.UnSubscribe(Events.PLAYER_INVENTORY_CHANGED_EVENT, OnPlayerInventoryChanged);
        }

        private void Update()
        {
            _spamTimer += Time.deltaTime;

            if (_controls.GetControlMode() == Controls.Mode.Gameplay && _allowInteractions)
            {
                UpdateInteractableAndSetActive();
            }
        }

        /// <summary>
        /// Checks for user input and if there are valid actions for anything that is within range or being dragged by the mouse.  Will update the PlayerController with the
        /// appropriate state for the interaction so be careful to not change the state after returning from this method.
        /// </summary>
        /// <returns>True if there an interaction has been started, false otherwise.</returns>
        public bool ProcessInput()
        {
            _interactionOccured = false;

            for (int i = 0; i < _interactablesAtSelectionPosition.Count; i++)
            {
                IInteractable current = _interactablesAtSelectionPosition[i].interactable;

                _actionList = current.GetValidActions();

                for (int j = 0; j < _actionList.Count; j++)
                {
                    _interactionOccured = _controls.UsingJoystick() ? CheckControllerInput(_actionList[j], current) : HandleKeyboardInput(_actionList[j], current);

                    if (_interactionOccured)
                        break;
                }

                if (_interactionOccured)
                    break;
            }

            // Check if we should use an equpped Item
            ProcessInputForEquippedItems();

            return _interactionOccured;
        }

        public void ProcessInputForEquippedItems()
        {
            if (_controls.UsingJoystick())
            {
                _interactionOccured |= CheckControllerInput(UserActions.USEMAINEQUIPPEDITEM, null); // "favorite" item
                _interactionOccured |= CheckControllerInput(UserActions.USESECONDARYEQUIPPEDITEM, null); // "highlighted" item
            }
            else
            {
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM1, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM2, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM3, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM4, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM5, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM6, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM7, null);
                _interactionOccured |= HandleKeyboardInput(UserActions.USEEQUIPPEDITEM8, null);
            }
        }

        private void UpdateInteractableAndSetActive()
        {
            IInteractable currentInteractable;

            // If player is holding button and currently interacting with something, just return as we don't want to update the nearest item
            if (_controls.UsingJoystick() && _controls.GetButton(UserActions.INTERACT) && !_selectedInteractable.IsNullOrDestroyed())
                return;

            if (_controls.UsingJoystick())
            {
                currentInteractable = GetNearestItem();
            }
            else
            {
                currentInteractable = GetItemUnderMouse();
            }

            // If the nearest interactable item has changed, switch which is active
            if (!_selectedInteractable.IsNullOrDestroyed() && _selectedInteractable != currentInteractable)
            {
                _selectedInteractable.SetInactive();
            }

            _selectedInteractable = currentInteractable;

            if (!_selectedInteractable.IsNullOrDestroyed() && _showToolTips)
            {
                _selectedInteractable.ShowTooltip();
            }
        }

        private bool CheckControllerInput(UserActions action, IInteractable interactable)
        {
            if (_spamTimer > 0 && _controls.GetButton(action))
            {
                switch (action.Name)
                {
                    case nameof(UserActions.USEITEM):
                        // TODO We don't have this logic built into the items yet
                        break;
                    case nameof(UserActions.INTERACT):
                        if (_controls.GetButtonDown(action))
                        {
                            interactable.PlayerInteraction(_player, action);
                        }
                        break;
                    case nameof(UserActions.DISMANTLE):
                        if (_controls.GetButtonDown(action))
                        {
                            ActiveInteractable = interactable;
                            EnterDismantlingMode();
                        }
                        break;
                    case nameof(UserActions.DROPITEM):
                        break;
                    case nameof(UserActions.GIVEITEM):
                        ActiveInteractable = interactable;
                        ActiveObject = ((MonoBehaviour)interactable).GetComponentInParent<IItemTopmostParent>().GetGameObject();

                        interactable.PlayerInteraction(_player, action);
                        break;
                    case nameof(UserActions.USEMAINEQUIPPEDITEM):
                    case nameof(UserActions.USESECONDARYEQUIPPEDITEM):
                        // Only use equipped if button tap, not if held down
                        if (_controls.GetButtonDown(action))
                            HandleUseEquippedItem(action);
                        break;
                    default:
                        Debug.LogWarning("No handler in HandleControllerInput for action: " + action);
                        break;
                }

                _spamTimer = -Constants.BUTTON_SPAM_DELAY;

                return true;
            }

            return false;
        }

        private bool HandleKeyboardInput(UserActions action, IInteractable interactable)
        {
            if (_controls.GetButtonDown(action))
            {
                ActiveInteractable = interactable;

                switch (action.Name)
                {
                    case nameof(UserActions.USEITEM):
                        // TODO We don't have this logic built into the items yet
                        break;
                    case nameof(UserActions.DROPITEM):
                        break;
                    case nameof(UserActions.USEEQUIPPEDITEM1):
                    case nameof(UserActions.USEEQUIPPEDITEM2):
                    case nameof(UserActions.USEEQUIPPEDITEM3):
                    case nameof(UserActions.USEEQUIPPEDITEM4):
                    case nameof(UserActions.USEEQUIPPEDITEM5):
                    case nameof(UserActions.USEEQUIPPEDITEM6):
                    case nameof(UserActions.USEEQUIPPEDITEM7):
                    case nameof(UserActions.USEEQUIPPEDITEM8):
                        if (_spamTimer > 0f)
                        {
                            HandleUseEquippedItem(action);
                        }
                        break;
                    case nameof(UserActions.DISMANTLE):
                        EnterDismantlingMode();
                        break;
                    case nameof(UserActions.INTERACT):
                        interactable.PlayerInteraction(_player, action);
                        break;
                    case nameof(UserActions.GIVEITEM):
                        interactable.PlayerInteraction(_player, action);
                        break;
                    default:
                        break;
                }

                _spamTimer = -Constants.BUTTON_SPAM_DELAY;
                return true;
            }

            return false;
        }

        private void EnterDismantlingMode()
        {
            _player.StartDismantling();
        }

        private Vector3 forwardDirection = Vector3.zero;

        /// <summary>
        /// Looks for items in front of the player and returns the most important one. (meant for controller gameplay)
        /// </summary>
        /// <returns>IInteractable object if found, null otherwise.</returns>
        private IInteractable GetNearestItem()
        {
            // If player is moving towards an item don't try and find a new interactable. If they move the player during the interact with item state it will switch
            // back to Move state and then we will select a new interactable here
            if (IsMovingTowardsItemAlready())
                return _selectedInteractable;

            Vector3 forwardDir = _player.Forward;

            // base offset direction on joystick instead of player forward so it still works when running against a wall

            float xAxis = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            float zAxis = _controls.GetAxis(Controls.MovementAxis.MoveVertical);
            float tresholdForUpdating = 0.4f;

            if (Mathf.Abs(xAxis) > tresholdForUpdating || Mathf.Abs(zAxis) > tresholdForUpdating)
            {
                Vector3 playerMovementDirection = UnityUtils.ConvertScreenVectorToIsometric(xAxis, zAxis).normalized;
                forwardDirection = playerMovementDirection;
            }

            forwardDir = forwardDirection;

            // Add a slight offset forward to select items in front of player first
            Vector3 playerPosition = _player.transform.position + forwardDir * forwardSearchBias;

#if UNITY_EDITOR
            if (_showInteractionPositionGizmo)
            {
                Debug.DrawLine(_player.transform.position, playerPosition, Color.cyan);
            }
#endif
            int hitCount = Physics.OverlapSphereNonAlloc(playerPosition, searchRange, _hitBuffer, _interactionLayers);

            return UpdateNearestInteracables(hitCount, playerPosition);
        }

        private bool IsMovingTowardsItemAlready()
        {
            if (_player.CurrentState is PlayerInteractWithItemState _)
                return true;

            if (_player.CurrentState is PlayerHarvestState _)
                return true;

            return false;
        }

        /// <summary>
        /// Looks for items close to the mouse position and returns the most important one. If no interactable is found, returns null.
        /// </summary>
        /// <param name="hitCount">The number of colliders hit to check from the buffer</param>
        /// <param name="hitBuffer">Array of Colliders to determine closest interactable from</param>
        /// <param name="fromPosition">Position to find nearest to</param>
        /// <returns>IInteractable object if found, null otherwise.</returns>
        private IInteractable GetItemUnderMouse()
        {
            Ray mouseRay = _controls.GetRayToPointerPosition();

            Vector3 mousePosition;
            if (Physics.Raycast(mouseRay, out RaycastHit hitInfo, 600f, _interactionLayers))
            {
                mousePosition = hitInfo.point;
            }
            else
            {
                mousePosition = _controls.GetPointerPositionOnGround();
            }

            int hitCount = Physics.SphereCastNonAlloc(mouseRay, mouseSearchRadius, _raycastHitBuffer, 600f, _interactionLayers);

            // Copy raycast buffer, into hit buffer
            for (int i = 0; i < hitCount; i++)
            {
                _hitBuffer[i] = _raycastHitBuffer[i].collider;
            }

            // Interactibles will mainly be ranked according to their distance from mousePosition, which is the closest to the camera
            return UpdateNearestInteracables(hitCount, mousePosition);
        }

        /// <summary>
        /// Updates the list of nearest interactable items to the fromPosition and returns the closest one.
        /// Uses the priority system to find items of higher priority over ones that may be slightly closer to position.
        /// </summary>
        /// <param name="hitCount">Number if items to check in the hit buffer</param>
        /// <param name="fromPosition">Position to find the nearest object from</param>
        private IInteractable UpdateNearestInteracables(int hitCount, Vector3 fromPosition)
        {
            _interactablesAtSelectionPosition.Clear();

            if (hitCount == 0)
            {
                return null;
            }

            for (int i = 0; i < hitCount; i++)
            {
                // Always null check the monobehaviour, not the interface
                IInteractable interactableScript = _hitBuffer[i].GetComponentInParent<IInteractable>();
                MonoBehaviour monoScript = interactableScript as MonoBehaviour;
                if (!monoScript.IsNullOrDestroyed() && monoScript.enabled)
                {
                    GameObject interactableGO = monoScript.gameObject;
                    float distance = Vector3.Distance(fromPosition, _hitBuffer[i].ClosestPoint(fromPosition));

                    if (!_interactablesAtSelectionPosition.Any(x => x.interactable == interactableScript))
                    {
                        InteractibleMetaData toInsert = new InteractibleMetaData();
                        toInsert.Set(interactableGO, interactableScript, distance);
                        _interactablesAtSelectionPosition.Add(toInsert);
                    }
                }
            }

            if (_interactablesAtSelectionPosition.Count > 0)
            {
                bool topPriority = _interactablesAtSelectionPosition.Any(x => x.priority == 1);
                IOrderedEnumerable<InteractibleMetaData> sortedInteractables;
                if (Controls.UsingController || topPriority)
                {
                    sortedInteractables = _interactablesAtSelectionPosition.OrderBy(x => x.priority).ThenBy(x => x.distance);
                }
                else
                {
                    sortedInteractables = _interactablesAtSelectionPosition.OrderBy(x => x.distance).ThenBy(x => x.priority);
                }
                _interactablesAtSelectionPosition = sortedInteractables.ToList();
            }

            InteractibleMetaData nearest = _interactablesAtSelectionPosition.FirstOrDefault();

            // nearest is not guaranteed here, I think we can pick up items in the process of being destroyed or recycled.
            if (hitCount > 1 && nearest != null)
            {
                Vector3 gridPosition = nearest.gameObject.transform.position.GetSnappedPosition(1);

                // Remove any interactables that are not at the grid position of the nearest selection
                for (int i = _interactablesAtSelectionPosition.Count - 1; i >= 0; i--)
                {
                    Vector3 interactablePosition = _interactablesAtSelectionPosition[i].gameObject.transform.position.GetSnappedPosition(1);

                    if (interactablePosition != gridPosition)
                    {
                        _interactablesAtSelectionPosition.RemoveAt(i);
                    }
                }
            }

            return nearest?.interactable;
        }

        /// <summary>
        /// Sets if item interaction tool tips should be shown when the item is in range.
        /// </summary>
        /// <param name="show"></param>
        public void SetShowToolTips(bool show)
        {
            _showToolTips = show;

            if (show && !_selectedInteractable.IsNullOrDestroyed())
                _selectedInteractable.ShowTooltip();
        }

        public void Success(ItemPile interactionItem, int affectedCount)
        {
            int removed = _inventory.Remove(interactionItem.item, affectedCount);

            if (removed != affectedCount)
                throw new UnityException("Can't remove enough items from inventory as required from item interaction.  This shouldn't happen");
        }

        /// <summary>
        /// Sets the spam timer to negative the time passed in, so no inputs will be processed until that time has passed
        /// </summary>
        /// <param name="time"></param>
        public void DelayInteraction(float time)
        {
            _spamTimer = -time;
        }

        public void AllowInteractions(bool allow)
        {
            _allowInteractions = allow;

            if (!allow)
            {
                if (_selectedInteractable != null)
                    _selectedInteractable.SetInactive();
                _selectedInteractable = null;
            }
        }

        /// <summary>
        /// Compares the two items to see if the new item is on a higher priority layer
        /// as the current item.  If the layers are the same, returns false.
        /// </summary>
        /// <param name="newItem">Item to compare current item against.</param>
        /// <param name="oldItem">Current priority item</param>
        /// <returns></returns>
        private static bool IsHigherPriority(GameObject newItem, GameObject oldItem)
        {
            //Priorities should go:
            //1.Collectable
            //2.Harvestable on planter (have to be above planters and probably bees too)
            //3.Player Mob
            //4.automation items
            //5.Harvestable
            //?.buildings

            if (oldItem == null)
                return true;
            if (newItem == null)
                return false;

            int newLayer = newItem.layer;
            int oldLayer = oldItem.layer;

            // If old is collectable, than its the highest level
            if (oldLayer == Layers.COLLECTABLE)
                return false;
            // If new item is collectable and old wasn't then its higher
            if (newLayer == Layers.COLLECTABLE)
                return true;
            // harvestable on planter takes priority over items on ground
            else if (oldLayer == Layers.HARVESTABLE && IsHarvestableOnPlanter(oldItem))
                return false;
            else if (newLayer == Layers.HARVESTABLE && IsHarvestableOnPlanter(newItem))
                return true;
            // If old is mob, than it is same or higher than new, return false
            else if (oldLayer == Layers.MOB)
                return false;
            // If new item is mob than it is higher, since current is NOT Collectable or Mob
            else if (newLayer == Layers.MOB && newItem.CompareTag(Tags.PLAYER_MOB))
                return true;
            // this layer contains the advanced items, tagged Automation - ore harvester must pass here, planter must fail
            else if (oldLayer == Layers.ITEM_ON_GROUND)
                return false;
            else if (newLayer == Layers.ITEM_ON_GROUND)
                return true;
            else if (oldLayer == Layers.HARVESTABLE) // TODO: check if we have a ore harvester, which would override
                return false;
            else if (newLayer == Layers.HARVESTABLE)
                return true;
            // Both items are planters?
            else
                return false;
        }

        // Mirrors order from IsHigherPriority but returns priority level as an integer for easy sorting of many items
        private static int GetPriority(GameObject go)
        {
            if (go == null)
                return 7;

            int layer = go.layer;

            if (layer == Layers.HARVESTABLE && IsHarvestableOnPlanter(go))
                return 1;
            else if (layer == Layers.COLLECTABLE)
                return 2;
            else if (layer == Layers.MOB && go.CompareTag(Tags.PLAYER_MOB))
                return 3;
            else if (layer == Layers.ITEM_ON_GROUND)
                return 4;
            else if (layer == Layers.HARVESTABLE)
                return 5;
            else
                return 6; // buildings and other stuff
        }

        private static bool IsHarvestableOnPlanter(GameObject go)
        {
            var harvestable = go.GetComponent<GrowingHarvestableVisuals>();
            if (harvestable == null)
                return false;
            else
                return harvestable.IsOnPlanter();
        }

        private void OnPlayerInventoryChanged()
        {

        }

        public void HandleUseEquippedItem(UserActions action)
        {
            // If in menu, don't listen for these events, they still might fire because they are used to equip items to slots in menu
            if (_controls.GetControlMode() == Controls.Mode.UI)
                return;

            int itemIndex;

            switch (action.Name)
            {
                case nameof(UserActions.USEEQUIPPEDITEM1):
                case nameof(UserActions.USEMAINEQUIPPEDITEM):
                    itemIndex = 0;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM2):
                    itemIndex = 1;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM3):
                    itemIndex = 2;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM4):
                    itemIndex = 3;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM5):
                    itemIndex = 4;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM6):
                    itemIndex = 5;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM7):
                    itemIndex = 6;
                    break;
                case nameof(UserActions.USEEQUIPPEDITEM8):
                    itemIndex = 7;
                    break;
                case nameof(UserActions.USESECONDARYEQUIPPEDITEM):
                    itemIndex = CurrentEquippedItemSlot;
                    break;
                default:
                    throw new UnityException("Invalid UserAction passed to HandleUseEquippedItem.  Action: " + action.ToString());
            }

            ItemPile equipPile = _inventory.GetEquippedPile(itemIndex);

            if (equipPile != null && equipPile.HasItems() && _inventory.GetCountOfItem(equipPile.item) > 0)
            {
                if (equipPile.item is AdvancedItem advItem)
                {
                    if (advItem.equipToPlayer)
                        _player.EquipItemOnPlayer(advItem);
                    else
                        _player.UseEquippedItem(this.transform.position, equipPile, this);
                }
                else if (equipPile.item is UseableItem useItem)
                {
                    useItem.UseItem(_player.gameObject);
                    _inventory.Remove(useItem);
                }
                else
                {
                    // Core item
                    _player.UseEquippedItem(transform.position, equipPile, this);
                }

                EquipppedItemUsed?.Invoke(this, new ItemEventArgs(_inventory, equipPile.item));
            }
        }

        // Gizmos

        public void OnDrawGizmos()
        {
#if UNITY_EDITOR
            if (UnityEditor.EditorApplication.isPlaying)
            {
                // Add a slight offset forward to select items in front of player first
                Vector3 playerPosition = _player.transform.position + _player.Forward * forwardSearchBias;

                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(playerPosition, searchRange);
            }
#endif
        }
    }
}