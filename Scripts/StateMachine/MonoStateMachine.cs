// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// State Machine using MonoBehaviour based states
    /// </summary>
    public class MonoStateMachine : MonoBehaviour
    {
        
        // PUBLIC

        public MonoState startState;

        
        // PRIVATE VARIABLES

        protected MonoState _currentState;

        
        // LIFECYCLE EVENTS

        protected virtual void Start()
        {
            ChangeState(startState);
        }

        protected virtual void Update()
        {
            MonoState nextState = _currentState.Run(this);

            if (nextState != _currentState)
                ChangeState(nextState);
        }


        // METHODS		

        public virtual void ChangeState(MonoState nextState)
        {
            if (_currentState != null)
            {
                _currentState.Exit(this);
            }

            _currentState = nextState;

            if (_currentState != null)
            {
                _currentState.Enter(this);
            }
        }

        public virtual MonoState GetCurrentState()
        {
            return _currentState;
        }
    }
}