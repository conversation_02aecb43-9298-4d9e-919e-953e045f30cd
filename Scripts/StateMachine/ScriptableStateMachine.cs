// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// State Machine using Scriptable Object based states.
    /// Only used for the playercontroller state machine for now.
    /// Using Scriptable Objects is not ideal for this so maybe avoid expanding our use of this system.
    /// </summary>
	public class ScriptableStateMachine : MonoBehaviour
    {
        // UNITY HOOKUP
        
        public State startState;


        // OTHER FIELDS

        protected State _currentState;


        // LIFECYCLE EVENTS

        protected virtual void Start()
        {
            ChangeState(startState);
        }

        private void Update()
        {
            State nextState = _currentState.Run(this) as State;

            if (nextState != _currentState)
                ChangeState(nextState);
        }


        // OTHER METHODS

        public void ChangeState(State nextState)
        {
            if (_currentState != null)
            {
                _currentState.Exit(this);
            }

            _currentState = nextState;

            if (_currentState != null)
            {
                _currentState.Enter(this);
            }

#if PLAYER_LOGGING
            Debug.LogFormat("{0} changing states.  To: {1}", gameObject.name, nextState.ToString()); 
#endif
        }
    }
}