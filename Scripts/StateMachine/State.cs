// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// Used with StateMachine class for scriptable object based states.
    /// Only used for the playercontroller state machine for now.
    /// Using Scriptable Objects is not ideal for this, so maybe avoid expanding our use of this system.
    /// </summary>
    public abstract class State : ScriptableObject, IState
    {
        public bool isInterruptable;

        public abstract void Enter(ScriptableStateMachine controller);

        public abstract IState Run(ScriptableStateMachine controller);

        public abstract void Exit(ScriptableStateMachine controller);
    }
}