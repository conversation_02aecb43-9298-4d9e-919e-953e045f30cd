// Copyright Isto Inc.
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.StateMachine
{
    /// <summary>
    /// State machine that allows pushing into a stack of states as well as normal state transitions.  Needs a start state to get
    /// going and then transitions can come from the state calling ChangeState() or if this class is extended, from the StateMachine
    /// calling ChangeState().  
    /// The start state is set in Editor
    /// </summary>
    public class MonoPushdownStateMachine : MonoStateMachine
    {
        // Private Variables

        protected Stack<MonoState> _stateStack;

        // Lifecycle Events

        protected virtual void Awake()
        {
            _stateStack = new Stack<MonoState>();
        }

        /// <summary>
        /// If a start state is set, enter it
        /// </summary>
        protected override void Start()
        {
            if (startState != null)
            {
                PushState(startState);
            }
        }

        /// <summary>
        /// Makes sure there is an active state and runs it's update
        /// </summary>
        protected override void Update()
        {
            if (_currentState == null)
            {
                Debug.LogWarning("Null state set in state machine: " + gameObject.name);
                PushState(startState);
            }

            _currentState?.Run(this);
        }

        // Methods		

        /// <summary>
        /// Pushes a state onto the stack and enters it.
        /// </summary>
        /// <param name="subState"></param>
        public virtual void EnterSubState(MonoState subState)
        {
            if (subState == null)
            {
                Debug.LogError("Trying to push null substate onto stack.  Current state: " + _currentState);
                return;
            }

            PushState(subState);
        }

        /// <summary>
        /// Pops the current state off the stack and exits it, setting the next state on the stack as
        /// the current state.
        /// </summary>
        public virtual void ExitSubState()
        {
            //Debug.Log("Exiting from sub state: " + _currentState.GetType().ToString());

            MonoState previous = PopState();

            _currentState.ReturnFromSubState(this, previous);
        }

        /// <summary>
        /// Internal method for pushing a state onto the stack
        /// </summary>
        /// <param name="state"></param>
        protected void PushState(MonoState state)
        {
            _currentState = state;

            _stateStack.Push(state);

            state.Enter(this);
        }

        /// <summary>
        /// Calls the Exit() method on the current state then pops it off the stack and updates the _currentState with the new 
        /// state.
        /// </summary>
        /// <returns>The MonoState that was popped.</returns>
        protected MonoState PopState()
        {
            _currentState.Exit(this);

            MonoState previous = _stateStack.Pop();

            if (_stateStack.Count == 0)
                _currentState = null;
            else
                _currentState = _stateStack.Peek();

            return previous;
        }

        public override void ChangeState(MonoState nextState)
        {
            if (_currentState != null)
            {
                PopState();
            }

            PushState(nextState);
        }
    }
}