// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Configuration;
using Isto.Core.Game;
using Isto.Core.Localization;
using Isto.Core.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Data
{
    public class AutoSaveManager : MonoBehaviour
    {
        // UNITY HOOKUP

        public bool disableAutoSaves; // allows you to turn it off manually in the scene
        public float minutesBetweenAutoSaves = 15f;
        public LocalizedString _gameAutoSavedMessageKey;
        public LocalizedString _gameAutoSaveFailedMessageKey;
        public static readonly string SAVE_FREQUENCY_PLAYER_PREFS_KEY = "SaveFrequencySetting";


        // OTHER FIELDS

        protected static readonly int MAX_CONCURRENT_AUTOSAVES = 3;
        protected bool _disabledFromEvents = true; // as the start of game logic completes
        private float _autoSaveInterval = 0f; // In seconds - 0 means "off"
        private float _autoSaveTimer;
        private bool _disabledFromSettings;


        // INJECTION

        private IGameData _gameData;
        private Settings _settings;
        private IUIMessages _userFeedbackMessages;

        [Inject]
        public void Inject(IGameData gameData, [Inject(Id = UIMessageHandlerType.Message)] IUIMessages messages, Settings settings)
        {
            _gameData = gameData;
            _userFeedbackMessages = messages;
            _settings = settings;
        }


        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            UpdateAutoSaveSettings();
            _autoSaveInterval = minutesBetweenAutoSaves * 60f;
        }

        protected virtual void Start()
        {
            RegisterEvents();
        }

        protected virtual void OnDestroy()
        {
            UnregisterEvents();
        }

        private void Update()
        {
            // There are certain times we don't want the auto save to be possible, so we pause the timer by aborting this method.
            // The conditions must inlcude any transition where we might fiddle with the player inventory, unlocks, or research.
            // A collateral bonus of this rule is that we will prevent lag spikes during these parts of the game, so this could
            // also justify aborting at certain sensitive times not mentioned here just to take advantage of that.
            // (note: lag spikes from saving are tolerable on PC, but probably much worse on xbox one)

            // These should protect us from running during: heart transitions, death and respawn animations, building first connections,
            // and giant cutscenes. Fast travel also implemented, but untested.
            // Not protected from: during supply pod battle (consider it?)
            if (disableAutoSaves || _disabledFromSettings || _disabledFromEvents || GameState.StoryAnimationActive)
                return;

            // Don't want autosaves to happen during the pause menu (and save menu), so use scaled time.
            // This also makes it easier to test autosaving by messing with the timescale.
            _autoSaveTimer += Time.deltaTime;

            if (_autoSaveTimer > _autoSaveInterval)
            {
                // going to also happen from the save event callback but I want to be certain we won't run this twice in a row
                ResetAutoSaveTimer();
                CreateAutoSave();
                UpdateAutoSaveSettings();
            }
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            // Note: be careful about GAME_SAVED events, they can easily overlap, which may or may not be fine in a
            // case by case basis. We have to make sure we don't end up running the autosave timer during cutscenes
            // that change something on the player.
            // For instance sequences like START START END END could happen and we're not really handling that.
            Events.Subscribe(Events.GAME_SAVED, OnGameSaved);

            GlobalGameplayEvents.MenuInteraction += OnMenuInteraction;
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.GAME_SAVED, OnGameSaved);
            GlobalGameplayEvents.MenuInteraction -= OnMenuInteraction;
        }

        protected void OnCutsceneStarted()
        {
            _disabledFromEvents = true;
        }

        protected void OnCutsceneEnded()
        {
            _disabledFromEvents = false;
        }

        private void OnGameSaved()
        {
            ResetAutoSaveTimer();
        }

        private void OnMenuInteraction(object sender, MenuInteractionEventArgs e)
        {
            bool isMenuWhatCanChangeSettings = e.menu == GameMenusEnum.GAME;

            if (isMenuWhatCanChangeSettings && e.action == MenuInteractionEventArgs.Action.Close)
            {
                UpdateAutoSaveSettings();
            }
        }

        // ACCESSORS

        public static int GetAutoSaveFrequencySetting()
        {
            int currentSetting;
            if (!UnityUtils.IsAutoSaveSupported())
            {
                // Autosave disabled on some platforms, so make this the default setting
                currentSetting = 3;
            }
            else
            {
                // Default selection is the 3rd setting, "15 min"
                currentSetting = PlayerPrefs.GetInt(SAVE_FREQUENCY_PLAYER_PREFS_KEY, 2);
            }
            return currentSetting;
        }

        public static void SetAutoSaveFrequencySetting(int value)
        {
            PlayerPrefs.SetInt(SAVE_FREQUENCY_PLAYER_PREFS_KEY, value);
        }

        /// <summary>
        /// Coroutine that tries to find an appropriate slot to store autosave data into.
        /// Autosaves take up normal save slots so we try to choose one of those that's available, unless we reach
        /// MAX_CONCURRENT_AUTOSAVES in which case we will reuse the oldest existing AUTOSAVE slot we find, or else choose #-1.
        /// </summary>
        /// <param name="onNumberFound">Delegate that will receive a slot number and a flag telling if the save will overwrite</param>
        /// <returns></returns>
        private IEnumerator GetAutoSaveSlotNumber(Action<int, bool> onNumberFound)
        {
            List<int> slots = null;
            bool waiting = true;
            _gameData.GetAllExistingSaveSlots(list => { slots = list; waiting = false; });

            while (waiting)
            {
                yield return null;
            }

            List<Tuple<int, GameStateSaveData>> autoSaves = new List<Tuple<int, GameStateSaveData>>();

            for (int i = 0; i < slots.Count; i++)
            {
                waiting = true;
                GameStateSaveData metaData = null;
                _gameData.LoadGameMetaData(slots[i], loadedData =>
                {
                    metaData = loadedData;
                    waiting = false;
                });
                while (waiting)
                {
                    yield return null;
                }
                if (metaData != null && metaData.isAutoSave)
                {
                    autoSaves.Add(new Tuple<int, GameStateSaveData>(slots[i], metaData));
                    if (autoSaves.Count >= MAX_CONCURRENT_AUTOSAVES)
                        break;
                }
            }

            if (autoSaves.Count < MAX_CONCURRENT_AUTOSAVES)
            {
                Debug.Log("Autosaves not maxed out, so getting a new autosave slot");
                // Pick a new slot
                _gameData.GetLowestAvailableSlotNumber(number =>
                {
                    bool isExistingSave = false;
                    if (number == -1 && autoSaves.Count > 0)
                    {
                        Tuple<int, GameStateSaveData> oldest = autoSaves.OrderBy(x => x.Item2.saveDate).First();
                        Debug.Log("GetLowestAvailableSlotNumber failed to get a new slot, so take an existing autosave slot; choosing #" + oldest.Item1);
                        number = oldest.Item1;
                        isExistingSave = true;
                    }

                    onNumberFound.Invoke(number, isExistingSave);
                });
            }
            else
            {
                Tuple<int, GameStateSaveData> oldest = autoSaves.OrderBy(x => x.Item2.saveDate).First();
                Debug.Log("Autosaves maxed out, so taking an existing autosave slot, choosing #" + oldest.Item1);
                onNumberFound.Invoke(oldest.Item1, true);
            }
        }


        // OTHER METHODS

        [ContextMenu("Save Now")]
        public void CreateAutoSave()
        {
            Events.RaiseEvent(Events.GAME_AUTOSAVING); // For indicator

            StartCoroutine(GetAutoSaveSlotNumber((slotNumber, isPreExistingSlot) =>
            {
                if (slotNumber < 0)
                {
                    Debug.LogError("Cannot create autosave. ");
                    _userFeedbackMessages.CreateMessage(Loc.Get(_gameAutoSaveFailedMessageKey));
                    return;
                }

                Debug.Log($"Creating autosave in slot#{slotNumber}. ");
                _gameData.SaveGameData(slotNumber, isPreExistingSlot, success =>
                {
                    if (success)
                    {
                        Events.RaiseEvent(Events.GAME_SAVED);
                        _userFeedbackMessages.CreateMessage(Loc.Get(_gameAutoSavedMessageKey));

                        _gameData.LoadGameMetaData(slotNumber, metaData =>
                        {
                            if (metaData == null)
                            {
                                Debug.LogError($"LoadGameMetaData failed to load slot#{slotNumber} after saving it");
                                _userFeedbackMessages.CreateMessage(Loc.Get(_gameAutoSaveFailedMessageKey));
                                return;
                            }

                            if (!metaData.isAutoSave)
                            {
                                metaData.isAutoSave = true;
                                // TODO: consider numbering them or otherwise identifying them to make them clearer to the users?
                                metaData.saveSlotName = "--AUTOSAVE--";
                                _gameData.SaveGameMetaData(slotNumber, metaData, success =>
                                {
                                    if (!success)
                                        Debug.LogWarning($"SaveGameMetaData for slot#{slotNumber} has failed to save metadata after turning the autosave flag ON ");
                                });
                            }
                        });
                    }
                    else
                    {
                        Debug.LogWarning($"SaveGameData failed for slot#{slotNumber} during autosave attempt. ");
                        _userFeedbackMessages.CreateMessage(Loc.Get(_gameAutoSaveFailedMessageKey));
                    }
                });
            }));
        }

        private void ResetAutoSaveTimer()
        {
            _autoSaveTimer = 0f;
        }

        private void UpdateAutoSaveSettings()
        {
            minutesBetweenAutoSaves = _settings.AutoSaveOptions[GetAutoSaveFrequencySetting()];
            _autoSaveInterval = minutesBetweenAutoSaves * 60f;
            _disabledFromSettings = minutesBetweenAutoSaves == 0;
        }
    }
}