// Copyright Isto Inc.
using Isto.Core.Game;
using Isto.Core.Installers;
using System;
using System.IO;
using System.Xml.Serialization;
using Zenject;

namespace Isto.Core.Data
{
    /// <summary>
    /// Handles meta data about the playthrough that we will preload before the rest of the save slot info to help with save slot selection
    /// </summary>
    public class GameStateDataManager : DataManager
    {
        public const string FILE_NAME = "gamestate";
        public const string FILE_PREFIX = "/" + FILE_NAME + "_";

        public override string FilePrefix => FILE_PREFIX;
        public override string BlobName => FILE_NAME;

        private GameState _gameState;
        private string _filename = "_data.xml";
        private IGameData _gameData;

        [Inject]
        public void Inject(GameState gameState, [Inject(Id = InjectId.SAVE_FILE_SUFFIX)] string saveFile, IGameData gameData)
        {
            _gameState = gameState;
            _gameData = gameData;
            _filename = saveFile;
        }

        /// <summary>
        /// Loads up the game state data for this save slot into the gamestatemanager so it will be available to it
        /// when we tell the game to load.
        /// This has to be done before the DI container bindings so we can have our settings ready.
        /// Therefore we do it manually at the opportune moment.
        /// </summary>
        public void PreLoad(int saveSlot, Action<GameStateSaveData> onLoaded)
        {
            _gameData.LoadGameMetaData(saveSlot, onLoaded);
        }

        /// <summary>
        /// Exists to mirror PreLoad and conveniently let us update specific parts of the GameStateData outside of the
        /// normal game save flow.
        /// </summary>
        public void PreSave(int saveSlot, GameStateSaveData data, Action<bool> onSaved)
        {
            _gameData.SaveGameMetaData(saveSlot, data, onSaved);
        }

        /// <summary>
        /// Not loading Game State Data at the same time as the other data files. See PreLoad.
        /// </summary>
        public override bool Load(in TextReader reader) { return true; }

        public override bool Validate(in TextReader reader, in object previousDataObject)
        {
            bool valid = false;

            GameStateSaveData previousData = previousDataObject as GameStateSaveData;
            GameStateSaveData loadedData = LoadXMLFile<GameStateSaveData>(reader);

            if (loadedData != null)
            {
                valid = previousData.Equals(loadedData);
            }

            return valid;
        }

        public override void Save(out object saveData)
        {
#if DATA_LOGGING
            Debug.Log("Saving Game State Data"); 
#endif
            GameStateSaveData data = _gameState.SaveGameStateData();
            saveData = data;
        }

        public override object GetSampleSaveData()
        {
            GameStateSaveData data = new GameStateSaveData();
            return data;
        }

        public override System.Type[] GetSaveExtraTypes()
        {
            return null;
        }

        public override void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer, out object saveData)
        {
            GameStateSaveData gameStateData = metaData;

            gameStateData.gameVersion = targetVersion;
            gameStateData.saveDate = System.DateTime.Now;

            serializer = GetXMLSerializer(gameStateData);
            saveData = gameStateData;
        }
    }
}
