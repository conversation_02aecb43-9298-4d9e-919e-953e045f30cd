// Copyright Isto Inc.

using FMOD;
using FMOD.Studio;
using FMODUnity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Debug = UnityEngine.Debug;
using STOP_MODE = FMOD.Studio.STOP_MODE;

namespace Isto.Core.Audio
{
    public class GameSounds : MonoBehaviour, IGameSounds
    {
        // UNITY HOOKUP

        [Header("Audio References")]
        public AudioTrackReferences _audioTrackReferences;


        // OTHER FIELDS

        // Tracks audio instances that are currently running.  Typically loopping audio
        protected readonly Dictionary<string, EventInstance> _runningInstances =
            new Dictionary<string, EventInstance>();

        private EventInstance _dialogueActiveSnapshot;
        private EventInstance _menuActiveSnapshot;
        private EventInstance _pauseMenuActiveSnapshot;

        // Tracks coroutines that are currently setting a parameters value.  Key is parameter value string
        private readonly Dictionary<string, Coroutine> _runningRoutines = new Dictionary<string, Coroutine>();


        // ACCESSORS

        public void SetGlobalParameter(string parameter, float value, float lerpTime = 0)
        {
            if (lerpTime == 0f)
            {
                RuntimeManager.StudioSystem.setParameterByName(parameter, value);
            }
            else
            {
                if (_runningRoutines.TryGetValue(parameter, out Coroutine activeRoutine))
                {
                    StopCoroutine(activeRoutine);
                    _runningRoutines.Remove(parameter);
                }

                _runningRoutines.Add(parameter,
                    StartCoroutine(SetGlobalParameterCoroutine(parameter, value, lerpTime)));
            }
        }

        public float GetGlobalParameter(string parameter)
        {
            if (RuntimeManager.StudioSystem.getParameterByName(parameter, out float paramValue) == RESULT.OK)
            {
                return paramValue;
            }

            Debug.LogError($"Unable to find global parameter {parameter} for FMOD.", this);
            return 0f;
        }

        public void SetSnapshot(string snapshotName)
        {
            SnapShots snapshot = (SnapShots)Enum.Parse(typeof(SnapShots), snapshotName);
            SetSnapshot(snapshot);
        }

        public void SetSnapshot(SnapShots snapShot)
        {
            //Debug.Log("Setting snapshot: " + snapShot);

            switch (snapShot)
            {
                case SnapShots.Gameplay:
                    _menuActiveSnapshot.stop(STOP_MODE.ALLOWFADEOUT);
                    _dialogueActiveSnapshot.stop(STOP_MODE.ALLOWFADEOUT);
                    _pauseMenuActiveSnapshot.stop(STOP_MODE.ALLOWFADEOUT);
                    break;
                case SnapShots.Menu:
                    _menuActiveSnapshot.start();
                    break;
                case SnapShots.Dialogue:
                    _menuActiveSnapshot.stop(STOP_MODE.ALLOWFADEOUT);
                    _dialogueActiveSnapshot.start();
                    break;
                case SnapShots.PauseMenu:
                    _pauseMenuActiveSnapshot.start();
                    break;
            }
        }

        private IEnumerator SetGlobalParameterCoroutine(string parameter, float value, float lerpTime)
        {
            float timer = 0f;
            float startValue = GetGlobalParameter(parameter);

            while (timer < lerpTime)
            {
                timer += Time.deltaTime;

                float nextValue = Mathf.Lerp(startValue, value, timer / lerpTime);
                RuntimeManager.StudioSystem.setParameterByName(parameter, nextValue);

                yield return null;
            }

            RuntimeManager.StudioSystem.setParameterByName(parameter, value);
        }


        // OTHER METHODS

        public void PlayOneShot(string eventRef, Vector3 position)
        {
            if (!string.IsNullOrEmpty(eventRef))
            {
                RuntimeManager.PlayOneShot(eventRef, position);
            }
        }

        public void PlayOneShot(SoundEventEnum soundEvent, Vector3 position)
        {
            EventReference? soundEventRef = null;

            switch (soundEvent)
            {
                case SettingsSoundEnum:
                    soundEventRef = _audioTrackReferences.SettingsSounds.FirstOrDefault(gameSoundRef => gameSoundRef.soundEnum == soundEvent.Value)?.eventRef;
                    break;
                case UISoundEnum:
                    soundEventRef = _audioTrackReferences.UISounds.FirstOrDefault(uiSoundRef => uiSoundRef.soundEnum == soundEvent.Value)?.eventRef;
                    break;
                case GameSoundEnum:
                    soundEventRef = _audioTrackReferences.GameSounds.FirstOrDefault(gameSoundRef => gameSoundRef.soundEnum == soundEvent.Value)?.eventRef;
                    break;
                default:
                    Debug.LogWarning($"Type of {soundEvent.GetType()} is not playable. Ensure that the SoundEventEnum is " +
                                     $"handled in GameSounds.cs.");
                    break;
            }

            if (soundEventRef.HasValue)
            {
                PlayOneShot(soundEventRef.Value, position);
            }
            else
            {
                Debug.LogWarning($"PlayOneShot cannot find matching event data for sound {soundEvent.Name}");
            }
        }

        public void PlayLoop(string eventRef, Vector3 position, string loopEndParameter = "FadeOut")
        {
            if (!_runningInstances.ContainsKey(eventRef))
            {
                _runningInstances.Add(eventRef, RuntimeManager.CreateInstance(eventRef));
            }
            else if (_runningInstances.ContainsKey(eventRef) && _runningInstances[eventRef].isValid() == false)
            {
                _runningInstances[eventRef] = RuntimeManager.CreateInstance(eventRef);
            }
            else
            {
                _runningInstances[eventRef].setParameterByName(loopEndParameter, 0f);
            }

            _runningInstances[eventRef].set3DAttributes(position.To3DAttributes());

            // If not already playing start looping audio
            if (_runningInstances[eventRef].getPlaybackState(out PLAYBACK_STATE playState) ==
                RESULT.OK)
            {
                if (playState != PLAYBACK_STATE.PLAYING)
                {
                    _runningInstances[eventRef].start();
                }
            }
        }

        public void PlayLoop(SoundEventEnum sfx, Vector3 position, string loopEndParameter = "FadeOut")
        {
        }

        public void StopLoop(string eventRef, string loopEndParameter = "FadeOut")
        {
            if (_runningInstances.ContainsKey(eventRef))
            {
                _runningInstances[eventRef].setParameterByName(loopEndParameter, 1f);
                _runningInstances[eventRef].stop(STOP_MODE.ALLOWFADEOUT);
                _runningInstances[eventRef].release();
            }
        }

        public void StopLoop(SoundEventEnum sfx, string loopEndParameter = "FadeOut")
        {
        }

        private void PlayOneShot(EventReference eventRef, Vector3 position)
        {
#if !CLOUD_BUILD
            RuntimeManager.PlayOneShot(eventRef, position);
#endif
        }
    }
}