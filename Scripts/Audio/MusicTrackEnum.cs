// Copyright Isto Inc.

using Isto.Core.Enums;

namespace Isto.Core.Audio
{
    public enum MusicTrack
    {
        NoMusic,
        Title,
        Random,
        EndGame
    }

    public class MusicTrackEnum : Int32Enum<MusicTrackEnum>
    {
        public static readonly MusicTrackEnum NO_MUSIC = new MusicTrackEnum(0, nameof(NO_MUSIC));
        public static readonly MusicTrackEnum RANDOM = new MusicTrackEnum(1, nameof(RANDOM));
        public static readonly MusicTrackEnum TITLE = new MusicTrackEnum(2, nameof(TITLE));
        public static readonly MusicTrackEnum END_GAME = new MusicTrackEnum(3, nameof(END_GAME));

        public MusicTrackEnum(int value, string name) : base(value, name)
        {
        }
    }
}