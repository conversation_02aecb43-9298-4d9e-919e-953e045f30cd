// Copyright Isto Inc.
using Isto.Core.StateMachine;

namespace Isto.Core.Cameras
{
    /// <summary>
    /// Used exclusively for our teaser trailer- we don't want the camera doing anything while
    /// we animate
    /// </summary>
    public class CameraDoNothingState : MonoState
    {

        // Lifecycle Events

        // Methods	

        private void Awake()
        {

        }

        public override void Enter(MonoStateMachine controller)
        {

        }

        public override void Exit(MonoStateMachine controller)
        {
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            throw new System.NotImplementedException("Sub states not supported on main camera rig.");
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return this;
        }
    }
}