// Copyright Isto Inc.
using UnityEngine;

namespace Isto.Core.Cameras
{
    public class LookAtCamera : MonoBehaviour
    {
        public bool DoOnUpdate = false;

        private void OnEnable()
        {
            Vector3 direction = Camera.main.transform.forward;
            direction.y = 0f;

            transform.LookAt(transform.position + direction);
        }

        private void Update()
        {
            if (DoOnUpdate)
                transform.LookAt(transform.position + Camera.main.transform.forward);
        }

    }
}