// Copyright Isto Inc.

using System.Collections.Generic;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Represents a localized expression that combines multiple localized terms.
    /// </summary>
    public class MultiTermLocExpression : LocExpression
    {
        public List<LocTerm> GetTerms()
        {
            return _locTerms;
        }

        public MultiTermLocExpression(List<LocTerm> locTerms)
        {
            _locTerms = locTerms;
        }
    }
}