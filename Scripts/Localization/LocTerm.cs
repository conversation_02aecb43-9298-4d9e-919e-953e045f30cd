// Copyright Isto Inc.

using TMPro;
using Zenject;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Abstract base class for localized terms. It takes a key and then returns the localized term for that key.
    /// </summary>
    public abstract class LocTerm
    {
        public enum LocalizationType { NonLocalized, Localized }

        public class Factory : PlaceholderFactory<LocalizationType, string, LocTerm> { }

        protected readonly string _key;

        [Inject]
        private ILocalizationProvider _localization;

        protected LocTerm(string locKey)
        {
            _key = locKey;
        }

        public string GetKey()
        {
            return _key;
        }

        public virtual string Localize()
        {
            return _localization.GetLocalizedText(_key);
        }
        
        public void LocalizeInto(TextMeshProUGUI tmpUGUI)
        {
            if (tmpUGUI != null)
            {
                tmpUGUI.text = Localize();
            }
        }
        
        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }

            LocTerm other = (LocTerm)otherObject;
            return _key.Equals(other._key);
        }
        
        public override int GetHashCode()
        {
            return _key != null ? _key.GetHashCode() : 0;
        }
    }
}