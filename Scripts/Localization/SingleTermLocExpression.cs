// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Represents a localized expression that will only ever have one term in it. This is useful for lists where
    /// some items may be complicated expressions but also contains a single one.
    /// </summary>
    public class SingleTermLocExpression : LocExpression
    {
        public LocTerm GetTerm()
        {
            return _locTerms.FirstOrDefault();
        }

        public SingleTermLocExpression(LocTerm locTerm)
        {
            _locTerms = new List<LocTerm>{locTerm};
        }
    }
}