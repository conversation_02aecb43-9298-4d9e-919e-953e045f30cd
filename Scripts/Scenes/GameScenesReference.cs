// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Isto.Core.Scenes
{
    [CreateAssetMenu(fileName = "CoreGameScenesReference", menuName = "Scriptables/Tools/Core Game Scenes Reference")]
    public class GameScenesReference : ScriptableObject
    {
        [SerializeField][SceneReference] protected string _applicationStartup = "0ApplicationStartup";
        [SerializeField][SceneReference] protected string _titleScreen = "1TitleScreen";
        [SerializeField][SceneReference] protected string _loadingScreen = "3Loading";
        [SerializeField][SceneReference] protected string _essentialsScene = "EssentialGameElementsScene";
        [SerializeField][SceneReference] protected string _startingAreaScene = "4Story-Mode";

        public virtual string ApplicationStartup => _applicationStartup;
        public virtual string TitleScreen => _titleScreen;
        public virtual string LoadingScreen => _loadingScreen;
        public virtual string EssentialsScene => _essentialsScene;
        public virtual string StartingAreaScene => _startingAreaScene;

        public virtual HashSet<string> DialogueScenes { get; protected set; }

        public bool IsDialogueScene(string sceneName)
        {
            return DialogueScenes.Contains(sceneName);
        }

        /// <summary>
        /// Be careful about using this method.
        /// In most circumstances we should have two scenes, one is the essentials, the other is the level.
        /// Consequently we'll look for the first loaded non-essentials scene and return that.
        /// This might vary in certain flows and this might simply not be true in certain projects.
        /// </summary>
        /// <returns>The name of the first currently loaded scene found that is not the Essentials scene.</returns>
        public virtual string GetCurrentSceneName()
        {
            string sceneName = "";
            for (int i = 0; i < SceneManager.sceneCount; i++)
            {
                if (SceneManager.GetSceneAt(i).name != EssentialsScene)
                {
                    sceneName = SceneManager.GetSceneAt(i).name;
                    break;
                }
            }
            return sceneName;
        }
    }
}