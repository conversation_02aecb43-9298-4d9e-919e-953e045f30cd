// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Beings;
using Isto.Core.StateMachine;
using System.Collections;
using UnityEngine;
using UnityEngine.Serialization;
using Zenject;

namespace Isto.Core.Items
{
    public class ItemPlaceCompleteState : MonoState
    {
        public static int PlacementsPending = 0;

        // Public Variable

        public bool PlaceAnimationComplete { get { return _animationComplete; } }
        public bool Active { get; private set; }

        [EventRef]
        public string placementSoundRef = @"event:/SFX/Atrio SFX_Automation Place-St";

        [Header("Disable After Placement")]
        [Tooltip("Array of objects to be disabled after placement is complete.  If GameObject it will be set inactive, if monobehavior it will be disabled.")]
        public UnityEngine.Object[] objectsToDisablePreDrop;
        [Tooltip("Array of objects to be disabled after placement is complete.  If GameObject it will be set inactive, if monobehavior it will be disabled.")]
        [FormerlySerializedAs("objectsToDisable")]
        public UnityEngine.Object[] objectsToDisablePostDrop;

        // Private Variables

        private ItemPlacementController _controller;
        private PlayerItemInteraction _interaction;
        private PlayerInventory _playerInventory;
        private AutomationPlayerController _playerController;
        private IGameSounds _gameSounds;
        private ItemPlaceState _placementState;
        private InteractableItem _itemComponent;

        // Juice
        private AdvItemDropParameters _dropParameters;

        private bool _animationComplete = true;

        // Lifecycle Events

        [Inject]
        public void Inject(PlayerInventory playerInventory, PlayerItemInteraction playerItemInteraction, AutomationPlayerController playerController,
            AdvItemDropParameters dropParam, IGameSounds gameSounds)
        {
            _playerInventory = playerInventory;
            _interaction = playerItemInteraction;
            _playerController = playerController;
            _dropParameters = dropParam;
            _gameSounds = gameSounds;
        }

        public override void Enter(MonoStateMachine controller)
        {
            Active = true;
            PlacementsPending++;

            _controller = controller as ItemPlacementController;
            _placementState = gameObject.GetComponent<ItemPlaceState>();
            _itemComponent = GetComponent<InteractableItem>();

            _interaction.DelayInteraction(_dropParameters.dropTime);

            // Subtract from player inventory
            _playerInventory.Remove(_controller.CurrentItem.item);

            StartCoroutine(AnimateDrop());

            _playerController.SetDropComplete(_controller.singlePlacement);

            _animationComplete = false;
        }

        public override void Exit(MonoStateMachine controller)
        {
            _animationComplete = true;
            Active = false;
            PlacementsPending--;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previous)
        {
            throw new UnityException("No handler for ReturnFromSubState in ItemPlaceCompleteState.  This state should not be pushing into sub states.");
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_animationComplete)
            {
                ActivateComponents();
                DisablePostDropObjects();
                _controller.ExitSubState();
            }
            return this;
        }

        // Methods

        private void ActivateComponents()
        {
            // Activate Scrips on this gameobject and children
            IActivatable[] scriptsToActivate = gameObject.GetComponentsInChildren<IActivatable>();

            for (int i = 0; i < scriptsToActivate.Length; i++)
            {
                scriptsToActivate[i].Activate();
            }
        }

        private void DisablePreDropObjects()
        {
            for (int i = 0; i < objectsToDisablePreDrop.Length; i++)
            {
                if (objectsToDisablePreDrop[i] is GameObject)
                    ((GameObject)objectsToDisablePreDrop[i]).SetActive(false);
                else if (objectsToDisablePreDrop[i] is MonoBehaviour)
                    ((MonoBehaviour)objectsToDisablePreDrop[i]).enabled = false;
            }
        }

        private void DisablePostDropObjects()
        {
            for (int i = 0; i < objectsToDisablePostDrop.Length; i++)
            {
                if (objectsToDisablePostDrop[i] is GameObject)
                    ((GameObject)objectsToDisablePostDrop[i]).SetActive(false);
                else if (objectsToDisablePostDrop[i] is MonoBehaviour)
                    ((MonoBehaviour)objectsToDisablePostDrop[i]).enabled = false;
            }
        }

        private IEnumerator AnimateDrop()
        {
            DisablePreDropObjects();

            // Hide tooltip while animating
            _controller.RemoveToolTip();

            // Hide line renderer if object has one (for heart tethered items)
            LineRenderer lineRend = GetComponent<LineRenderer>();

            if (lineRend != null)
                lineRend.enabled = false;

            GameObject placeHolder = CreatePlaceHolderItem();

            Vector3 endPosition = transform.position;
            Vector3 startPosition = endPosition + (Vector3.up * _dropParameters.height);

            float timer = 0f;

            while (timer < _dropParameters.dropTime)
            {
                Vector3 nextPosition = Vector3.Lerp(startPosition, endPosition, _dropParameters.curve.Evaluate(timer / _dropParameters.dropTime));

                transform.position = nextPosition;

                timer += Time.deltaTime;

                yield return null;
            }

            transform.position = endPosition;

            _gameSounds.PlayOneShot(placementSoundRef, transform.position);

            Instantiate(_dropParameters.dropParticleObject, transform.position, Quaternion.identity);

            // If object has line renderer, turn back on
            if (lineRend != null)
                lineRend.enabled = true;

            yield return null;

            Destroy(placeHolder);

            _animationComplete = true;
        }

        private GameObject CreatePlaceHolderItem()
        {
            // Create a temp gameobject to hold the space for the item
            GameObject placeHolder = new GameObject("PlaceHolder");

            placeHolder.layer = Layers.BUILDING;

            Vector3 itemCenter = transform.position + Constants.GRID_CENTERING_OFFSET;

            if (_placementState)
            {
                //Might have to consider not only the item's center but also its girth to avoid allowing a nimble player from overlapping 2 items
                itemCenter = _placementState.FindItemCenter(_itemComponent);
            }

            placeHolder.transform.position = itemCenter;
            SphereCollider placeHolderCollider = placeHolder.AddComponent<SphereCollider>();
            placeHolderCollider.isTrigger = true;

            float colliderRadius = 0.25f;

            // If advanced item adjust collider size based on largest of width/depth of item
            if (_itemComponent != null && _itemComponent.itemPile != null)
            {
                if (_itemComponent.itemPile.item is AdvancedItem advItem)
                    colliderRadius = Mathf.Max(advItem.depth, advItem.width) * 0.4f;
            }

            placeHolderCollider.radius = colliderRadius;

            return placeHolder;
        }
    }
}