// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Beings;
using Isto.Core.Inputs;
using Isto.Core.Pooling;
using Isto.Core.StateMachine;
using System.Collections;
using UnityEngine;
using Zenject;

namespace Isto.Core.Items
{
    public class EraseCompleteState : MonoState
    {
        // Public Variable

        [EventRef]
        public string eraseSoundRef = @"event:/SFX/Atrio SFX_Automation Place-St";

        // Visually this doesn't change much as this value should be configured to represent the average mouse delta required to get to the next tile
        // This gives the user some leeway to start their mouse drag with a small hiccup and still get their intended direction recorded
        [Range(0f, 1f)]
        [Tooltip("How far the mouse can go before drag direction gets locked in")]
        public float minDragScreenHeightPercent = 0.05f;

        // Private Variables

        private bool _blockingPlacementFlow;
        private bool _animationComplete;

        private Vector2 _mouseOrigin;
        private Vector3 _playerOrigin;

        // Cache

        private ItemPlacementController _controller;
        private Eraser _eraser;

        // Injected

        private PlayerItemInteraction _interaction;
        private AutomationPlayerController _playerController;
        private AdvItemDropParameters _dropParameters;
        private IGameSounds _gameSounds;
        private IControls _controls;
        private PooledItemFactory _pooledItemFactory;

        // Lifecycle Events

        [Inject]
        public void Inject(PlayerItemInteraction playerItemInteraction, AutomationPlayerController playerController,
            AdvItemDropParameters dropParam, IGameSounds gameSounds, IControls controls, PooledItemFactory pooledItemFactory)
        {
            _interaction = playerItemInteraction;
            _playerController = playerController;
            _dropParameters = dropParam;
            _gameSounds = gameSounds;
            _controls = controls;
            _pooledItemFactory = pooledItemFactory;
        }

        public override void Enter(MonoStateMachine controller)
        {
#if DISMANTLE_LOGGING
            Debug.Log($"EraseCompleteState.Enter on {gameObject.name}", gameObject);
#endif
            StopAllCoroutines();

            _controller = controller as ItemPlacementController;
            _eraser = gameObject.GetComponent<Eraser>();

            _controller.RemoveToolTip();

            _blockingPlacementFlow = true;
            _animationComplete = false;

            _mouseOrigin = _controls.GetPointerPosition();
            _playerOrigin = _playerController.transform.position;

            // Don't proceed to line placement unless user really goes for it
            float pixelDelta = Screen.height * minDragScreenHeightPercent;
            bool dragging = IsDragging();

            bool dismantlePressed = _controls.GetButton(PlayerDismantleState.DismantleInput);

            if (dragging || !_controller.IsFirstOfFastPlacement || !dismantlePressed)
            {
#if DISMANTLE_LOGGING
                Debug.Log($"EraseCompleteState.Enter: unblocking placement flow pre-emptively for {this.gameObject.name}; dragging={dragging} !first={!_controller.IsFirstOfFastPlacement} !dismantle={!dismantlePressed}", this.gameObject);
#endif
                // Unblock game logic but wait for FinishRemoval() to commit to our deletion
                _playerController.SetDismantleComplete(stopDropping: false);
                _blockingPlacementFlow = false;
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
#if DISMANTLE_LOGGING
            Debug.Log("EraseCompleteState.Exit", this.gameObject);
#endif
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previous)
        {
            throw new UnityException("No handler for ReturnFromSubState in ItemPlaceCompleteState.  This state should not be pushing into sub states.");
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_blockingPlacementFlow)
            {
#if DISMANTLE_LOGGING
                Debug.Log("EraseCompleteState.Run: blocking placement flow", this.gameObject);
#endif
                // Don't proceed to line placement unless user really goes for it
                bool dragging = IsDragging();
                bool dismantlePressed = _controls.GetButton(PlayerDismantleState.DismantleInput);

                if (dragging || !dismantlePressed)
                {
#if DISMANTLE_LOGGING
                    Debug.Log("EraseCompleteState.Run: unblocking!", this.gameObject);
#endif
                    // Unblock game logic but wait for FinishRemoval() to commit to our deletion
                    _playerController.SetDismantleComplete(stopDropping: false);
                    _blockingPlacementFlow = false;
                }
                else
                {
                    UserActions dismantleAbort = UserActions.CANCELDISMANTLING;

                    if (_controls.GetButton(dismantleAbort))
                    {
#if DISMANTLE_LOGGING
                        Debug.Log("EraseCompleteState.Run: abort", this.gameObject);
#endif
                        _playerController.SetDismantleCancelled();
                    }
                }
            }

            if (_animationComplete)
            {
#if DISMANTLE_LOGGING
                Debug.Log("EraseCompleteState.Run: animation complete, end!", this.gameObject);
#endif
                _controller.ExitSubState();
                _pooledItemFactory.ReturnPooledAdvancedItem(_eraser);
            }

            return this;
        }

        // Methods

        /// <returns>True if the eraser removed something</returns>
        public bool FinishRemoval(bool batched)
        {
            bool removed = false;
#if DISMANTLE_LOGGING
            Debug.LogWarning($"EraseCompleteState.FinishRemoval (batched={batched}) from {gameObject.name} onto {_eraser.EraseTarget.name}", _eraser.EraseTarget);
#endif
            if (_playerController.InteractionState.HandlePickupItem(_eraser.EraseTarget))
            {
                StartCoroutine(AnimateRemoval(batched));
                removed = true;
            }
            else
            {
                // Abort
                Eraser.UnreadyObjectForDismantling(_eraser.EraseTarget.gameObject);
                _pooledItemFactory.ReturnPooledAdvancedItem(_eraser);
            }

            return removed;
        }

        private IEnumerator AnimateRemoval(bool batched)
        {
            if (!batched)
            {
                _gameSounds.PlayOneShot(eraseSoundRef, transform.position);
            }

            GameObject particle = Instantiate(_dropParameters.dropParticleObject, transform.position, Quaternion.identity);
            particle.transform.localScale = Vector3.one * 0.25f;

            Vector3 endPosition = transform.position;
            Vector3 startPosition = endPosition + (Vector3.up * _dropParameters.height * 0.5f);

            float timer = 0f;

            while (timer < _dropParameters.dropTime)
            {
                Vector3 nextPosition = Vector3.Lerp(endPosition, startPosition, _dropParameters.curve.Evaluate(timer / _dropParameters.dropTime));

                transform.position = nextPosition;

                timer += Time.deltaTime;

                yield return null;
            }

            transform.position = startPosition;

            yield return null;

            _animationComplete = true;

#if DISMANTLE_LOGGING
            Debug.Log("EraseCompleteState.AnimateRemoval: end of coroutine", this.gameObject);
#endif
        }

        private bool IsDragging()
        {
            if (_controls.UsingJoystick())
            {
                Vector3 movement = _playerController.transform.position - _playerOrigin;

                // Square magnitude of .25 translates to 0.5 of actual movement
                return movement.sqrMagnitude > 0.25f;
            }
            else
            {
                float pixelDelta = Screen.height * minDragScreenHeightPercent;
                return (_controls.GetPointerPosition() - _mouseOrigin).sqrMagnitude > Mathf.Pow(pixelDelta, 2f);
            }
        }
    }
}