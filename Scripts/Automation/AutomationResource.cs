// Copyright Isto Inc.
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationResource
    {
        public event EventHandler<AutomationResourceEventArgs> Consumed;
        public event EventHandler<AutomationResourceEventArgs> GrowthComplete;
        public event EventHandler<AutomationPullEventArgs> PullStart;
        public event EventHandler PullEnd;

        public string ID;
        public float Health { get; private set; }
        public HarvestableItem Item { get; private set; }
        public float HarvestPercent { get { return 1 - (_healthTillNextDrop / _itemDropIncrement); } }
        public float GrowthPercent { get { return _currentGrowTime / _totalGrowTime; } }
        public bool IsFullyGrown { get { return GrowthPercent >= 1; } }
        public bool IsHarvestable { get { return IsFullyGrown || (Item.droppedItemsWhileGrowing.Count > 0 && _harvestableWhileGrowing); } }
        public bool IsReservedByPickerPal => ReservationHiveAddress != null;

        public AutomationGridSpace Space { get; internal set; }

        public AutomationCreatureHive ReservationHiveAddress = null;
        public bool VisualCulling = true;

        [Inject] private AutomationSystem _automationsystem;
        [Inject] private AutomationCoreItem.Factory _itemFactory = default;

        private float _maxHealth;
        private float _itemDropIncrement = 100;
        private float _healthTillNextDrop;
        private float _totalGrowTime = 10f;
        private float _currentGrowTime = 0f;
        private bool _harvestableWhileGrowing;
        private bool _isInfinite;

        public AutomationResource(AutomationResourceParams resourceParams)
        {
            HarvestableItem itemType = resourceParams.item;

            if (itemType.itemID.Equals("NO ID SET", StringComparison.CurrentCultureIgnoreCase))
                Debug.LogWarning($"No id set for Resource {itemType.ToString()}");

            Item = itemType;
            ID = itemType.itemID;

            _harvestableWhileGrowing = resourceParams.harvestableWhileGrowing;
            _maxHealth = itemType.harvestHP;
            _totalGrowTime = itemType.growingTime == 0f ? 0.1f : itemType.growingTime;
            _currentGrowTime = resourceParams.currentGrowthPercent * _totalGrowTime;
            _isInfinite = resourceParams.infiniteResource;

            _itemDropIncrement = itemType.harvestAmountPerItemDrop == 0 ? itemType.harvestHP : itemType.harvestAmountPerItemDrop;

            SetRemainingHealth(resourceParams.currentHealth);
        }

        public void Tick(float deltaTime)
        {
            if (GrowthPercent < 1)
            {
                TickGrowing(deltaTime);
            }
        }

        private void TickGrowing(float deltaTime)
        {
            _currentGrowTime = Mathf.Clamp(_currentGrowTime + deltaTime, 0, _totalGrowTime);

            if (_currentGrowTime >= _totalGrowTime)
                GrowthComplete?.Invoke(this, new AutomationResourceEventArgs(AutomationResourceEventArgs.EventType.FullyGrown));
        }

        public void SetRemainingHealth(float health)
        {
            Health = health;

            if (Item.harvestAmountPerItemDrop == 0)
                _healthTillNextDrop = health;
            else
                _healthTillNextDrop = Item.harvestAmountPerItemDrop;
        }

        public void Remove()
        {
            if (Space != null)
            {
                if (Space.itemProcessor.IsNullOrDestroyed() && Space.ActiveItem == null)
                    _automationsystem.DeleteGridSpace(Space);
                else if (Space.Sleeping)
                    _automationsystem.Grid.WakeUpSleepingSpace(Space);
            }

            Space = null;

            Consumed?.Invoke(this, new AutomationResourceEventArgs(AutomationResourceEventArgs.EventType.Deleted));
        }

        /// <summary>
        /// Processes damage done to the Resource.  If damage crosses the threshold to drop an item,
        /// returns true and sets the dropped items in the AutomationCoreItem queue parameter.
        /// </summary>
        /// <param name="amount">Amount of damage</param>
        /// <param name="droppedItems">Queue to hold any dropped items from resource after taking damage.</param>
        /// <returns>False if no item was dropped, true if one or more was.</returns>
        public bool TakeDamage(float amount, ref Queue<AutomationCoreItem> droppedItems)
        {
            Health = Mathf.Clamp(Health - amount, 0f, _maxHealth);

            _healthTillNextDrop -= amount;

            if (_healthTillNextDrop <= 0f || Health == 0f)
            {
                droppedItems.Clear();

                List<ItemPile> itemsToDrop = GetItemsToDrop();

                for (int i = 0; i < itemsToDrop.Count; i++)
                {
                    bool shouldDrop = UnityEngine.Random.value <= itemsToDrop[i].dropPercentage;

                    if (shouldDrop)
                    {
                        droppedItems.Enqueue(_itemFactory.Create(new CoreItemParams(itemsToDrop[i].item.itemID, itemsToDrop[i].count, Vector3.zero)));
                    }
                }

                // Not zeroing out to carry over any extra damage past the threshold for next time
                _healthTillNextDrop += _itemDropIncrement;

                // Restore health to max if infinite resource
                if (_isInfinite)
                    Health = _maxHealth;

                // If this was not a fully grown item, set it's health to zero to ensure it's destroyed.
                if (GrowthPercent < 1)
                    Health = 0f;

                if (Health == 0f)
                {
                    if (Space != null && Space.Sleeping)
                        _automationsystem.Grid.WakeUpSleepingSpace(Space);

                    Consumed?.Invoke(this, new AutomationResourceEventArgs(AutomationResourceEventArgs.EventType.FullyHarvested));
                }

                return true;
            }

            return false;
        }

        public void StartTornatoadPull(Vector3 directionOfPull)
        {

            PullStart?.Invoke(this, new AutomationPullEventArgs(directionOfPull));
        }

        public void EndTornatoadPull()
        {
            PullEnd?.Invoke(this, EventArgs.Empty);
        }

        private List<ItemPile> GetItemsToDrop()
        {
            if (GrowthPercent < 1 && Item.droppedItemsWhileGrowing.Count > 0)
                return Item.droppedItemsWhileGrowing;
            else
                return Item.droppedItems;
        }

        public class Factory : PlaceholderFactory<AutomationResourceParams, AutomationResource> { }
    }

    public struct AutomationResourceParams
    {
        public HarvestableItem item;
        public float currentHealth;
        public float currentGrowthPercent;
        public bool harvestableWhileGrowing;
        public bool infiniteResource;

        public AutomationResourceParams(HarvestableItem item, float currentHealth, float currentGrowthPercent, bool infiniteResource = false)
        {
            this.item = item;
            this.currentHealth = currentHealth;
            this.currentGrowthPercent = currentGrowthPercent;
            this.harvestableWhileGrowing = false;
            this.infiniteResource = infiniteResource;
        }
    }
}