// Copyright Isto Inc.
using Isto.Core.Items;
using System;
using System.Linq;
using UnityEngine;
using System.Xml.Serialization;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

using Debug = UnityEngine.Debug;

namespace Isto.Core.Automation
{
    [Serializable, XmlRoot("AutomationSystem")]
    public class AutomationSystem
    {
        public enum SystemComponents { ItemProcessor, Item, Resource }

        // Events

        /// <summary>
        /// Any time the system is changed (item added or removed) this event is fired so visual systems can update what is shown to user.
        /// </summary>
        public event EventHandler<AutomationSystemChangedEventArgs> SystemChanged;

        /// <summary>
        /// Event for knowing when an Processor or Resource is added to the system.  Should not be listened to for visual system!
        /// </summary>
        public event EventHandler<PositionEventArgs> ItemAdded;

        // Public Properties

        public float ItemMoveTime { get { return _moveTime; } }
        public AutomationGrid Grid { get { return _grid; } }
        public AutomationCoreItem.Factory CoreItemFactory { get { return _itemFactory; } }

        // Private Variables

        private AutomationCoreItem.Factory _itemFactory;
        private AutomationResource.Factory _resourceFactory;

        private float _moveTime;
        private AutomationGrid _grid;
        private AutomationGridSpace[] _activeSpaceBuffer;

        // Quad Tree Stuff

        public AutomationQuad Tree { get { return _gridQuadTree; } }

        // Quad tree needs to be powers of 2 since it's integer based and each area is divded by 2 as you go down
        private AutomationQuad _gridQuadTree = new AutomationQuad(new Vector3Int(-256, 0, 256), new Vector3Int(768, 0, -768));

        // Lifecycle Events

        // I removed the grid initialization from the parameterless constructor because zenject relies on this for default construction
        // even if it has no [Inject] flag and thus its gets called a ton of times if you start the scene.
        // Furthermore I don't think we ever actually need to use one such instance of AutomationSystem that was built this way, so
        // no need to initialize anything in there. If we do need really need it, keep in mind that it will get called a ton of times.
        public AutomationSystem() { }

        // The real constructor
        public AutomationSystem(float moveTime, AutomationCoreItem.Factory itemFactory, AutomationResource.Factory resourceFactory)
        {
            _grid = new AutomationGrid(false);

            _moveTime = moveTime;
            _itemFactory = itemFactory;
            _resourceFactory = resourceFactory;

            _activeSpaceBuffer = new AutomationGridSpace[10000];
        }

        public void TickSystem(float deltaTime)
        {
            //Debug.Log("Tick: " + deltaTime);

            ValidateBufferSize();

            // Need to copy to buffer incase the collection is changed while iterating over it
            _grid.ActiveSpaces.CopyTo(_activeSpaceBuffer, 0);

            int numOfSpaces = _grid.ActiveSpaces.Count;

            for (int i = 0; i < numOfSpaces; i++)
            {
                _activeSpaceBuffer[i].Tick(deltaTime);

                if (ShouldSpaceSleep(_activeSpaceBuffer[i]))
                {
                    _grid.PutSpaceToSleep(_activeSpaceBuffer[i]);
                }
            }
        }

        /// <summary>
        /// Checks the copy buffer size to make sure the automation system hasn't overgrown it. If so it increases the size
        /// </summary>
        private void ValidateBufferSize()
        {
            if (_grid.ActiveSpaces.Count > _activeSpaceBuffer.Length)
            {
                Debug.LogWarning("Active space buffer needs expansion. Increasing space cound by 5000");

                AutomationGridSpace[] expandedBuffer = new AutomationGridSpace[_activeSpaceBuffer.Length + 5000];

                for (int i = 0; i < _activeSpaceBuffer.Length; i++)
                {
                    expandedBuffer[i] = _activeSpaceBuffer[i];
                }

                _activeSpaceBuffer = expandedBuffer;
            }
        }

        private bool ShouldSpaceSleep(AutomationGridSpace space)
        {
            // If space has only resource on it sleep, unless it has no health, then keep awake so it gets removed by the Tick on the AutomationGridSpace
            if (space.Resource != null && space.Resource.IsFullyGrown && space.itemProcessor == null && space.Resource.Health > 0)
            {
                return true;
            }

            return false;
        }

        public bool DoesSpaceExist(float x, float z)
        {
            return _grid.DoesSpaceExist(x, z);
        }

        public bool DoesSpaceExist(Vector3 worldPosition)
        {
            return _grid.DoesSpaceExist(worldPosition.x, worldPosition.z);
        }

        //public bool TryAddGridSpace(AutomationGridSpace space)
        //{
        //    if (_grid.DoesSpaceExist(space.position.x, space.position.z))
        //        return false;

        //    return _grid.TryAddToGrid(space);
        //}

        //public void RemoveGridSpace(AutomationGridSpace space)
        //{
        //    _grid.RemoveSpace(space);
        //}

        //public Vector3 RotateGridSpace(Vector3 position)
        //{
        //    // If nothing on space, just return
        //    if (!DoesSpaceExist(position)) return default;

        //    AutomationGridSpace space = default;

        //    if(_grid.TryGetSpace(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z), ref space))
        //    {
        //        if(space.itemProcessor != null)
        //        {
        //            IDirectionalItem directional = space.itemProcessor as IDirectionalItem;

        //            if(directional != null)
        //            {
        //                // Rotate by 90 degrees
        //                directional.SetForwardDirection(Quaternion.Euler(0, 90, 0) * directional.GetDirection());

        //                SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, AutomationSystemChangedEventArgs.Change.Rotate));

        //                return directional.GetDirection();
        //            }
        //        }
        //    }

        //    return default;
        //}

        public bool TryAddResource(Vector3 position, HarvestableItem itemType, bool triggerEvent = true, bool visualCulling = true, float growthPercent = 0)
        {
            // Create an internal representation of the resource to add it in the automation system
            AutomationResource resource = _resourceFactory.Create(new AutomationResourceParams(itemType, itemType.harvestHP, growthPercent, itemType.infiniteResrouce));

            return TryAddResource(position, resource, triggerEvent, visualCulling);
        }

        public bool TryAddResource(Vector3 position, AutomationResource resource, bool triggerEvent = true, bool visualCulling = true)
        {
            AutomationGridSpace space = GetOrCreateGridSpace(position);

            if (space.TrySetResource(resource, visualCulling))
            {
                if (triggerEvent)
                    // Create the visual representation of the resource
                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, AutomationSystemChangedEventArgs.Change.AddResource));

                ItemAdded?.Invoke(this, new PositionEventArgs(space.position, resource.ID));

                return true;
            }

            return false;
        }

        public bool TryRemoveResource(Vector3 position)
        {
            if (DoesSpaceExist(position))
            {
                AutomationGridSpace gridSpace = default;

                if (_grid.TryGetSpace(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z), ref gridSpace))
                {
                    gridSpace.ClearResource();

                    if (gridSpace.IsEmpty())
                    {
                        DeleteGridSpace(gridSpace);
                    }

                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(gridSpace, AutomationSystemChangedEventArgs.Change.RemoveResource));
                    return true;
                }

                Debug.LogWarning("Couldn't retrieve space even though IsSpaceOccupied returned true.");
                return false;
            }

            return false;
        }

        /// <summary>
        /// Item Processor's are things like Factory, Harvester, Grav pipe.  Any automation item that handles interacting with items
        /// </summary>
        /// <param name="position"></param>
        /// <param name="processor"></param>
        /// <param name="forward"></param>
        /// <param name="triggerEvent"></param>
        /// <returns></returns>
        public bool TryAddItemProcessor(Vector3 position, IItemProcessor processor, Vector3 forward = default, bool triggerEvent = false)
        {
            AutomationGridSpace space = GetOrCreateGridSpace(position);

            if (space.itemProcessor.IsNullOrDestroyed())
            {
                if (processor is IAutomationGridSpaceUser gridUser)
                    gridUser.SetGridSpace(space);

                if (forward != default && processor is IDirectionalItem directional)
                    directional.SetForwardDirection(forward);

                if (space.Sleeping)
                    _grid.WakeUpSleepingSpace(space);
                else if (processor is AutomationGenericProcessor && space.Resource == null)
                    _grid.PutSpaceToSleep(space);
                else if (processor is AutomationBlockedSpace)
                    _grid.PutSpaceToSleep(space);

                space.itemProcessor = processor;

                // From what I see this event trigger is always false and this is the only place where AddProcessor events would exist
                // so that flow for item creation is not in use at all anymore? at least not in gameplay
                if (triggerEvent)
                {
                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, AutomationSystemChangedEventArgs.Change.AddProcessor));
                }

                ItemAdded?.Invoke(this, new PositionEventArgs(space.position, processor.ProcessorID, space));

                return true;
            }

            return false;
        }

        /// <summary>
        /// Item Processors are things like Factory, Harvester, Grav pipe.  Any automation item that handles interacting with items.
        /// This positioning algorithm has been designed to help the backpack dropping flow avoid placing it in bad spots.
        /// It therefore relies on related considerations about what is acceptable or not. See also comments in the method.
        /// </summary>
        /// <param name="position"></param>
        /// <param name="processor"></param>
        /// <param name="forward"></param>
        /// <param name="triggerEvent"></param>
        /// <returns>The position of the nearest available space found</returns>
        public Vector3? AddItemProcessorToNearestSpace(Vector3 position, IItemProcessor processor, Vector3 forward = default, bool triggerEvent = false)
        {
            Vector3? finalPosition = null;

            // Let's do this in a slightly dumb way for now... if there is a noticeable performance hit we can adjust then
            List<Vector3> possibilities = new List<Vector3>();
            for (float i = -Constants.BACKPACK_DROP_MAX_DISTANCE; i < Constants.BACKPACK_DROP_MAX_DISTANCE + 0.5f; i++)
            {
                for (float j = -Constants.BACKPACK_DROP_MAX_DISTANCE; j < Constants.BACKPACK_DROP_MAX_DISTANCE + 0.5f; j++)
                {
                    possibilities.Add(new Vector3(position.x + i, position.y, position.z + j));
                }
            }

            IOrderedEnumerable<Vector3> prioritized = possibilities.OrderBy(x => (position - x).sqrMagnitude);
            foreach (Vector3 possibility in prioritized)
            {
                if (TryAddItemProcessor(possibility, processor, forward, triggerEvent))
                {
                    finalPosition = possibility;
                    break;
                }
            }

            // This should occur very rarely if at all considering the range we cover, and considering that some tiles (like
            // those with lightbulbs and buildings) cannot have processors placed on them normally, but that this process
            // will allow it, creating more opportunities to place the item (which I consider a ok result for the backpack).
            if (finalPosition == null)
            {
                // Note that if it does happen then the backpack will still work but it will not be registered in automation and
                // therefore will not register to culling either.
                Debug.LogError($"AddItemProcessorToNearestSpace was not able to find a free space near {position} to place {processor.ProcessorID}");
            }

            return finalPosition;
        }

        public bool TryRemoveProcessor(float x, float z)
        {
            if (DoesSpaceExist(Mathf.RoundToInt(x), Mathf.RoundToInt(z)))
            {
                AutomationGridSpace gridSpace = default;

                if (_grid.TryGetSpace(Mathf.RoundToInt(x), Mathf.RoundToInt(z), ref gridSpace))
                {
                    if (gridSpace.itemProcessor != null && gridSpace.itemProcessor is IAutomationInventory inventoryComponent)
                    {
                        IInventory inv = inventoryComponent.GetInventory();

                        // Remove the processor first, so the item drops dont get blocked by the processor
                        gridSpace.RemoveProcessor();

                        DropAllInventoryItems(inv, gridSpace.position);
                    }
                    else
                    {
                        gridSpace.RemoveProcessor();
                    }

                    if (gridSpace.IsEmpty())
                    {
                        DeleteGridSpace(gridSpace);
                    }

                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(gridSpace, AutomationSystemChangedEventArgs.Change.RemoveProcessor));
                    return true;
                }

                Debug.LogWarning("Couldn't retrieve space even though IsSpaceOccupied returned true.");
                return false;
            }

            return false;
        }

        public bool TryRemoveProcessor(Vector3 position)
        {
            return TryRemoveProcessor(position.x, position.z);
        }

        public bool TryAddCoreItemToSpace(Vector3 gridSpacePosition, AutomationCoreItem item, bool autoCollectItem = false, bool triggerEvent = true, bool ignoreProcessor = false)
        {
            AutomationGridSpace space = GetOrCreateGridSpace(gridSpacePosition);

            if (autoCollectItem || space.TrySetItem(item, ignoreProcessor))
            {
                if (triggerEvent)
                {
                    AutomationSystemChangedEventArgs.Source changeSource = autoCollectItem ? AutomationSystemChangedEventArgs.Source.Player : AutomationSystemChangedEventArgs.Source.ItemProcessor;
                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, item, AutomationSystemChangedEventArgs.Change.AddCoreItem, changeSource));
                }

                return true;
            }

            return false;
        }

        public bool TryAddCoreItemToSpace(AutomationGridSpace space, AutomationCoreItem item, bool triggerEvent = true)
        {
            if (space.TrySetItem(item))
            {
                AutomationSystemChangedEventArgs.Source changeSource = AutomationSystemChangedEventArgs.Source.ItemProcessor;

                if (triggerEvent)
                {
                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, item, AutomationSystemChangedEventArgs.Change.AddCoreItem, changeSource));
                }

                return true;
            }

            return false;
        }

        public bool TryAddCoreItem(Vector3 position, CoreItem item, int count = 1, bool autoCollectItem = false, bool triggerEvent = true)
        {
            AutomationCoreItem newItem = _itemFactory.Create(new CoreItemParams(item.itemID, count, position));

            return TryAddCoreItemToSpace(position, newItem, autoCollectItem, triggerEvent);
        }

        // Note that this method is referred to in some commented out automation test functions
        // Also note that it is only attempting to delete the Active item
        // Also ideally this should be refactored to use DeleteCoreItem(AutomationCoreItem item) to avoid code duplication
        public bool TryDeleteCoreItem(Vector3 position)
        {
            if (DoesSpaceExist(position))
            {
                AutomationGridSpace gridSpace = default;

                if (_grid.TryGetSpace(position.x, position.z, ref gridSpace))
                {
                    AutomationCoreItem deletedItem = gridSpace.ActiveItem;

                    gridSpace.ConsumeItem(gridSpace.ActiveItem);

                    if (gridSpace.IsEmpty())
                    {
                        DeleteGridSpace(gridSpace);
                    }

                    SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(gridSpace, AutomationSystemChangedEventArgs.Change.RemoveCoreItem) { changedItem = deletedItem });
                    return true;
                }

                Debug.LogWarning("Couldn't retrieve space even though IsSpaceOccupied returned true.");
                return false;
            }

            return false;
        }

        public bool DeleteCoreItem(AutomationCoreItem item)
        {
            bool success = false;

            AutomationGridSpace space = default;

            Vector3 itemPosition;

            if (item.GridSpace != null)
            {
                itemPosition = item.GridSpace.position;
            }
            else
            {
                itemPosition = item.IsMoving ? item.TargetPosition : item.position;
            }

            if (_grid.TryGetSpace(itemPosition.x, itemPosition.z, ref space) && space.ContainsItem(item))
            {
                space.ConsumeItem(item);
                SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, item, AutomationSystemChangedEventArgs.Change.RemoveCoreItem));

                if (space.IsEmpty())
                {
                    DeleteGridSpace(space);
                }

                success = true;
            }
            else  // Item is not on space, so probably getting autocollected but needs to have visuals cleaned up
            {
#if UNITY_EDITOR && AUTOMATION_SETUP_LOGGING
                Debug.LogWarning($"item {item.ID} on space {itemPosition} cannot be deleted because it is not in the automation system. Attempting to clean up visuals.");
#endif
                SystemChanged?.Invoke(this, new AutomationSystemChangedEventArgs(space, item, AutomationSystemChangedEventArgs.Change.RemoveCoreItem));
            }

            return success;
        }

        public void DropAllInventoryItems(IInventory inventory, Vector3 position, int dropRadius = 0, bool autoCollect = true)
        {
            for (int i = 0; i < inventory.Items.Count; i++)
            {
                ItemPile nextPile = inventory.Items[i];

                if (nextPile.HasItems())
                {
                    Vector3 dropPosition = position + UnityUtils.GetNearestAxisDirection(UnityUtils.RandomXZVector(1)) * dropRadius;

                    AutomationCoreItem coreItem = _itemFactory.Create(new CoreItemParams(nextPile.item.itemID, nextPile.count, dropPosition));

                    if (!TryAddCoreItemToSpace(dropPosition, coreItem, autoCollect))
                    {

                        // If unable to drop on position, try dropping without using radius
                        if (!TryAddCoreItemToSpace(position, coreItem, autoCollect))
                        {
                            Debug.LogError("Couldn't drop inventory item on to grid space at " + dropPosition);
                        }
                    }
                }
            }
        }

        public bool CanMoveItemToGridSpace(AutomationGridSpace destination, AutomationCoreItem item, out int spaceAvailable)
        {
            spaceAvailable = 0;

            if (destination.Resource != null && !(destination.itemProcessor is AutomationPlanter))
                return false;

            // If there is an item processor on the space, return if it's available or not
            if (!destination.itemProcessor.IsNullOrDestroyed())
            {
                spaceAvailable = destination.itemProcessor.IsAvailable(item);

                return spaceAvailable > 0;
            }

            bool noExistingItem = destination.ActiveItem == null;

            if (noExistingItem)
            {
                spaceAvailable = item.count;
                return true;
            }
            else
            {
                return false;
            }
        }

        public AutomationGridSpace GetOrCreateGridSpace(Vector3 position)
        {
            AutomationGridSpace gridSpace = default;

            if (_grid.TryGetSpace(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z), ref gridSpace))
                return gridSpace;
            else
            {
                AutomationGridSpace newSpace = new AutomationGridSpace(position);

                _gridQuadTree.Insert(new QuadTreeNode(Vector3Int.RoundToInt(position), newSpace));

                if (_grid.TryAddToGrid(newSpace))
                    return newSpace;
                else
                {
                    Debug.LogError("Cannot add grid space to expected empty space.");
                    return newSpace;
                }
            }
        }

        public AutomationGridSpace GetOrCreateGridSpace(Vector3Int position)
        {
            AutomationGridSpace gridSpace = default;

            if (_grid.TryGetSpace(position.x, position.z, ref gridSpace))
                return gridSpace;
            else
            {
                AutomationGridSpace newSpace = new AutomationGridSpace(position);

                _gridQuadTree.Insert(new QuadTreeNode(Vector3Int.RoundToInt(position), newSpace));

                if (_grid.TryAddToGrid(newSpace))
                    return newSpace;
                else
                {
                    Debug.LogError("Cannot add grid space to expected empty space.");
                    return newSpace;
                }
            }
        }

        public bool TryGetExistingGridSpace(Vector3Int position, out AutomationGridSpace space)
        {
            space = null;

            return _grid.TryGetSpace(position.x, position.z, ref space);
        }

        public bool TryGetExistingGridSpace(Vector3 position, out AutomationGridSpace space)
        {
            space = null;

            return _grid.TryGetSpace(Mathf.RoundToInt(position.x), Mathf.RoundToInt(position.z), ref space);
        }

        public bool TryGetExistingGridSpace(int x, int z, out AutomationGridSpace space)
        {
            space = null;

            return _grid.TryGetSpace(x, z, ref space);
        }

        public void DeleteGridSpace(AutomationGridSpace space)
        {
            Tree.Remove(Vector3Int.RoundToInt(space.position));
            _grid.RemoveSpace(space);
        }

        public override string ToString()
        {
            return _grid.ToString();
        }

        public static bool DoesSpaceContainAnyItem(List<string> itemList, AutomationGridSpace space, bool includePickerPalReservedResources = true)
        {
            bool hasResource = space.Resource != null && space.Resource.IsFullyGrown && (includePickerPalReservedResources || !space.Resource.IsReservedByPickerPal);

            if (!hasResource && space.AllItemsOnSpace.Count == 0)
                return false;

            for (int i = 0; i < itemList.Count; i++)
            {
                if (hasResource && space.Resource.ID.Equals(itemList[i]))
                    return true;
                else
                {
                    for (int j = 0; j < space.AllItemsOnSpace.Count; j++)
                    {
                        if (space.AllItemsOnSpace[j].ID.Equals(itemList[i]))
                            return true;
                    }
                }
            }

            return false;
        }

        public static bool DoesSpaceContainAnyItem(HashSet<string> itemList, AutomationGridSpace space, bool includePickerPalReservedResources = true)
        {
            bool hasResource = space.Resource != null && space.Resource.IsFullyGrown && (includePickerPalReservedResources || !space.Resource.IsReservedByPickerPal);

            if (!hasResource && space.AllItemsOnSpace.Count == 0)
                return false;

            if (hasResource && itemList.Contains(space.Resource.ID))
                return true;

            for (int i = 0; i < space.AllItemsOnSpace.Count; i++)
            {
                if (itemList.Contains(space.AllItemsOnSpace[i].ID))
                    return true;
            }

            return false;
        }

        // Using hive reference as reservation system for the picker pal to be able to have a mutex on plants (will work as long as we have only 1 pal per hive)
        public static bool DoesSpaceContainAnyHarvestableWithoutReservations(HashSet<string> itemList, AutomationGridSpace space, AutomationCreatureHive homeAddress)
        {
            bool doesIt = space.Resource != null
                       && space.Resource.IsHarvestable
                       && itemList.Contains(space.Resource.ID)
                       && (!space.Resource.IsReservedByPickerPal || space.Resource.ReservationHiveAddress == homeAddress);
            return doesIt;
        }

        public static bool DoesSpaceContainHarvestable(AutomationGridSpace space)
        {
            bool doesIt = space.Resource != null;  //&& space.Resource.IsHarvestable;

            return doesIt;
        }

        public List<AutomationGridSpace> GetAllActiveGridSpacesInRadius(Vector3 centerPoint, float radius)
        {
            List<AutomationGridSpace> gridSpaceList = new List<AutomationGridSpace>();

            int xDistance = Mathf.RoundToInt(radius);
            int zDistance = Mathf.RoundToInt(radius);

            for (int i = -xDistance; i < xDistance; i++)
            {
                for (int j = -zDistance; j < zDistance; j++)
                {
                    Vector3 point = new Vector3(i, 0, j) + centerPoint;

                    if (TryGetExistingGridSpace(point, out AutomationGridSpace space))
                        gridSpaceList.Add(space);

                    //Debug.DrawLine(centerPoint, point, Color.cyan, 2f);
                }
            }

            return gridSpaceList;
        }

        //public int GetAllActiveGridSpacesInRadius(Vector3 centerPoint, float radius, ref List<AutomationGridSpace> spacesBuffer)
        //{
        //    spacesBuffer.Clear();

        //    // Note: it would be more performant to use a simple cast. can we use a custom rounding method that returns (int)x+0.5f depending on sign?
        //    Vector3Int roundedCenterPoint = new Vector3Int(Mathf.RoundToInt(centerPoint.x), Mathf.RoundToInt(centerPoint.y), Mathf.RoundToInt(centerPoint.z));
        //    int xDistance = Mathf.RoundToInt(radius);
        //    int zDistance = Mathf.RoundToInt(radius);

        //    int totalSpaces = 0;

        //    for (int i = -xDistance; i < xDistance; i++)
        //    {
        //        for (int j = -zDistance; j < zDistance; j++)
        //        {
        //            Vector3Int point = new Vector3Int(i, 0, j) + roundedCenterPoint;

        //            if (TryGetExistingGridSpace(point, out AutomationGridSpace space))
        //            {
        //                spacesBuffer.Add(space);
        //                totalSpaces++;
        //            }

        //            //Debug.DrawLine(centerPoint, point, Color.cyan, 2f);
        //        }
        //    }

        //    return totalSpaces;
        //}

        //public bool DoesAnyItemExistInRadius(Vector3 centerPoint, float radius, List<string> itemIDs)
        //{
        //    if (itemIDs == null || itemIDs.Count == 0) 
        //        return false;

        //    int xDistance = Mathf.RoundToInt(radius);
        //    int zDistance = Mathf.RoundToInt(radius);

        //    int centerX = Mathf.RoundToInt(centerPoint.x);
        //    int centerZ = Mathf.RoundToInt(centerPoint.z);

        //    for (int i = -xDistance; i < xDistance; i++)
        //    {
        //        for (int j = -zDistance; j < zDistance; j++)
        //        {
        //            Vector3 point = new Vector3(i, 0, j) + centerPoint;

        //            if (TryGetExistingGridSpace(i + centerX, j + centerZ, out AutomationGridSpace space))
        //            {
        //                for (int z = 0; z < itemIDs.Count; z++)
        //                {
        //                    if (space.IsItemOnSpace(itemIDs[z]))
        //                        return true;
        //                }
        //            }
        //        }
        //    }

        //    return false;
        //}


#if UNITY_EDITOR
        [ContextMenu("Print Grid Contents")]
        public void PrintGrid()
        {
            if (EditorApplication.isPlaying)
            {
                Debug.Log(ToString());
            }
        }
#endif
    }

    public class AutomationSystemChangedEventArgs : EventArgs
    {
        public enum Change { AddResource, RemoveResource, AddProcessor, RemoveProcessor, Rotate, AddCoreItem, RemoveCoreItem }
        public enum Source { Player, ItemProcessor }

        public Change changeType;
        public Source changeSource;
        public AutomationGridSpace space;
        public AutomationCoreItem changedItem;

        public AutomationSystemChangedEventArgs(AutomationGridSpace space, Change changeType, Source changeSource = Source.ItemProcessor)
        {
            this.space = space;
            this.changeType = changeType;
            this.changeSource = changeSource;
        }

        public AutomationSystemChangedEventArgs(AutomationGridSpace space, AutomationCoreItem item, Change changeType, Source changeSource = Source.ItemProcessor)
        {
            this.space = space;
            this.changedItem = item;
            this.changeType = changeType;
            this.changeSource = changeSource;
        }
    }

    public class PositionEventArgs : EventArgs
    {
        public Vector3 position;
        public string itemID;
        public AutomationGridSpace space;

        public PositionEventArgs(Vector3 position, string itemID, AutomationGridSpace space = null)
        {
            this.position = position;
            this.itemID = itemID;
            this.space = space;
        }
    }

}