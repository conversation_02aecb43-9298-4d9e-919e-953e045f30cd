// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationBlockedSpaceRegister : MonoBehaviour
    {
        [SerializeField] private Vector2Int area;

#if UNITY_EDITOR
        public Color gizmoColor = Color.white;
#endif

        private AutomationSystem _automationSystem;

        [Inject]
        public void Inject(AutomationSystem automationSystem)
        {
            _automationSystem = automationSystem;
        }

        public void OnEnable()
        {
            Vector3 startPosition = transform.position;

            for (int i = 0; i < area.x; i++)
            {
                for (int j = 0; j < area.y; j++)
                {
                    Vector3 position = transform.position + Vector3.right * i + Vector3.forward * j;

                    AutomationGridSpace space = _automationSystem.GetOrCreateGridSpace(position);

                    if (space.itemProcessor is AutomationBlockedSpace _)
                        return;

                    if (!_automationSystem.TryAddItemProcessor(position, new AutomationBlockedSpace()))
                        Debug.LogWarning($"Could not block automation space at {position}.  There may be an existing item there already");
                }
            }
        }

        public void OnDisable()
        {
            Vector3 startPosition = transform.position;

            for (int i = 0; i < area.x; i++)
            {
                for (int j = 0; j < area.y; j++)
                {
                    Vector3 position = transform.position + Vector3.right * i + Vector3.forward * j;

                    _automationSystem.TryRemoveProcessor(position);
                }
            }
        }

#if UNITY_EDITOR
        public void OnDrawGizmosSelected()
        {
            for (int i = 0; i < area.x; i++)
            {
                for (int j = 0; j < area.y; j++)
                {
                    Vector3 position = transform.position + Vector3.right * i + Vector3.forward * j;

                    Gizmos.color = gizmoColor;
                    Gizmos.DrawCube(position + Constants.GRID_CENTERING_OFFSET, Vector3.one);
                }
            }
        }
#endif

        public void OnValidate()
        {
            if (area.x < 0)
                area.x = 0;
            if (area.y < 0)
                area.y = 0;
        }
    }
}