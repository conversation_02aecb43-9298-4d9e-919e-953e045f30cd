// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Items;
using Isto.Core.ObjectCulling;
using Isto.Core.Pooling;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UPool;
using Zenject;

namespace Isto.Core.Automation
{
    public class AutomationSystemDisplay : MonoBehaviour, IDataLoadCompleteHandler
    {
        public enum AddedObjectType { Other, ItemProcessor, Ressource }

        public Vector3 BottomCorner => _bottomCorner;
        public int CullingAreaGridDiameter => _cullingAreaGridDiameter;

        [SerializeField] private Vector3 _bottomCorner = new Vector3(-200, 0, -200);

        [Header("Display Item Containers")]
        [SerializeField] private Transform _resourcesContainer = default;
        [SerializeField] private Transform _processorsContainer = default;
        [SerializeField] private Transform _coreItemContainer = default;

        private CullingStaticObjectsController _cullingController;
        private Vector3[] _gridPositionsBuffer = new Vector3[400];              // Culling area grid diameter squared
        [SerializeField] private int _cullingAreaGridDiameter = 10;
        [SerializeField] private int _cullingSpheresPerRow = 80;

        [Header("Wrong visual flag hack fix")]
        // These are serialized as Visible but they should not have been. Ideally we'd fix the data but for now just ignore them.
        [SerializeField] private List<Item> _brokenVisualFlagItemsToFix = default;

        private Dictionary<int, Coroutine> _runningShowObjectsRoutines = new Dictionary<int, Coroutine>();
        private bool _dataLoadComplete = false;

        // Injected

        private AutomationSystem _system;
        private MasterItemList _masterItemList;
        private PooledItemFactory _pooledItemFactory;

        [Inject]
        public void Inject(AutomationSystem automationSystem, MasterItemList masterItemList, PooledItemFactory pooledItemFactory)
        {
            _system = automationSystem;
            _masterItemList = masterItemList;
            _pooledItemFactory = pooledItemFactory;
        }

        private void OnEnable()
        {
            _system.SystemChanged += OnSystemChanged;

            _cullingController = new CullingStaticObjectsController(_cullingAreaGridDiameter, _cullingSpheresPerRow, _bottomCorner, Camera.main, CullingGroupStateChanged);

            InitializeMovingCullingGroup();
        }

        private void Update()
        {
            UpdateMovingItemsSpherePositions();
        }

        private void OnDisable()
        {
            _system.SystemChanged -= OnSystemChanged;

            _cullingController.CullingGroup.Dispose();

            _movingItemsCullingGroup.Dispose();
            _movingItemsCullingGroup = null;
        }

        [ContextMenu("ForceRefreshVisibleItems")]
        public void RefreshVisibleItems()
        {
#if DISPLAY_SYSTEM_LOGGING
            Debug.Log("Display. Data load complete");
#endif
            //If coming from save, the AutomationSystem will be loaded after all the culling groups are setup, so we need to manually show the first visible items
            int[] visibleIndices = new int[_cullingController.Spheres.Length];
            int visibleCount = _cullingController.CullingGroup.QueryIndices(true, visibleIndices, 0);

            for (int i = 0; i < visibleCount; i++)
            {
                int positionCount = _cullingController.GetGridPositionsInBoundingArea(visibleIndices[i], ref _gridPositionsBuffer);

                // Need to copy buffer so it isn't overwritten while running coroutine to create the objects
                Vector3[] positions = (Vector3[])_gridPositionsBuffer.Clone();

#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"Refreshing Sphere: {visibleIndices[i]}");
#endif

                HideObjectsInArea(visibleIndices[i]);

                Coroutine co = StartCoroutine(ShowObjectsInArea(positions, positionCount));

                _runningShowObjectsRoutines[visibleIndices[i]] = co;
            }
        }

        private void OnSystemChanged(object sender, AutomationSystemChangedEventArgs e)
        {
            bool spaceOnScreen = e.space != null ? _cullingController.IsSpaceVisible(e.space.position) : _cullingController.IsSpaceVisible(e.changedItem.position);

            switch (e.changeType)
            {
                case AutomationSystemChangedEventArgs.Change.AddResource:
                    if (spaceOnScreen)
                    {
                        if (_masterItemList.TryGetItemByID(e.space.Resource.ID, out HarvestableItem newItem))
                        {
                            if (IsItemAssetValid(newItem))
                            {
                                //CreateDisplayPrefabAsync(newItem.addressableAsset, e.space, _resourcesContainer);

                                if (e.space.Resource.VisualCulling)
                                {
                                    TryGetPooledDisplayPrefab(newItem, e.space, _resourcesContainer, AddedObjectType.Ressource);
                                    e.space.ResourceRendered = true;
                                }
                            }
                            else
                                Debug.LogWarning($"Trying to create resource in automation system but {e.space.Resource.ID} asset is invalid.");
                        }
                        else
                            Debug.LogWarning($"Trying to create resource in automation system but {e.space.Resource.ID} item not found in masteritemlist.");
                    }
                    // If we're not on screen, the culling system will show us when we get close enough
                    break;
                case AutomationSystemChangedEventArgs.Change.AddProcessor:
                    if (spaceOnScreen)
                    {
                        AdvancedItem processorItem = _masterItemList.GetItemByID<AdvancedItem>(e.space.itemProcessor.ProcessorID);

                        if (processorItem != null && IsItemAssetValid(processorItem))
                        {
                            TryGetPooledDisplayPrefab(processorItem, e.space, _processorsContainer, AddedObjectType.ItemProcessor);
                            e.space.ProcessorRendered = true;
                        }
                        else
                            Debug.LogWarning($"Trying to create processor in automation system but {e.space.itemProcessor.ProcessorID} item not found in masteritemlist.  Processor type: {e.space.itemProcessor.ToString()}");
                    }
                    // If we're not on screen, the culling system will show us when we get close enough
                    break;
                case AutomationSystemChangedEventArgs.Change.AddCoreItem:
                    int cullingIndex = GetNextMovingItemIndexAndIncrement();

                    _movingItemToIndexMap.Add(e.changedItem, cullingIndex);

                    if (spaceOnScreen)
                    {
                        // This is not perfect, but at least whatever is sitting in automation will look lined up as it should be
                        bool bounce = e.space == null || e.space.itemProcessor == null;
                        CreateItemDisplayPrefabAsync(e.changedItem, cullingIndex, e.changedItem.position, _coreItemContainer, autoCollect: e.changeSource == AutomationSystemChangedEventArgs.Source.Player, bounce);
                    }
                    // If off screen, track in moving items dictionary.  If on screen, this is done once async object creation is completed
                    else
                    {
                        if (_movingItems.ContainsKey(cullingIndex))
                        {
                            Debug.LogError($"Item at index {cullingIndex} exists already.  Item: {_movingItems[cullingIndex].ID}");
                            break;
                        }

                        // Track the item in the moving spheres culling group
                        _movingItems.Add(cullingIndex, e.changedItem);
                        _movingSpheres[cullingIndex].position = e.changedItem.position;
                        _movingSpheres[cullingIndex].radius = _movingItemSphereRadius;

#if DISPLAY_SYSTEM_LOGGING
                        Debug.Log($"CoreItem added. OnScreen:{spaceOnScreen}. Index:{cullingIndex}. MovingItems: {_movingItems.Count}, MovingItemToIndexMap: {_movingItemToIndexMap.Count}");
#endif
                    }

                    break;
                case AutomationSystemChangedEventArgs.Change.RemoveCoreItem:
                    // If item tracked by moving items culling, remove it from tracking
                    if (_movingItemToIndexMap.TryGetValue(e.changedItem, out int sphereIndex))
                    {
                        _movingItems.Remove(sphereIndex);
                        _movingItemToIndexMap.Remove(e.changedItem);

                        if (_movingDisplayObjects.TryGetValue(sphereIndex, out GameObject displayObject))
                        {
#if DISPLAY_SYSTEM_LOGGING
                            Debug.Log("Deleting object at: " + displayObject.transform.position);
#endif

                            ItemController controller = displayObject.GetComponent<ItemController>();
                            controller.DestroyGameObject(); // Will check for pooling support first

                            _movingDisplayObjects.Remove(sphereIndex);
                        }

                        // Add index back into possible index queue
                        _movingItemIndexes.Enqueue(sphereIndex);

#if DISPLAY_SYSTEM_LOGGING
                        Debug.Log($"CoreItem removed. Index:{sphereIndex}");
#endif
                    }
                    else
                    {
                        // If already consumed, it was probably spawned and dumped directly into a container or processor, so wasn't added to the map
                        if (!e.changedItem.IsConsumed)
                        {
                            Debug.LogWarning($"Attempted to remove a CoreItem that is not part of the moving item map. Flagging it to be destroyed instead.");
                            e.changedItem.ConsumeItem();
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        private bool IsItemAssetValid(Item newItem)
        {
            if (newItem.addressableAsset == null)
            {
                Debug.LogError("AddressableAsset not set for " + newItem.itemID);
                return false;
            }
            else if (!newItem.addressableAsset.RuntimeKeyIsValid())
            {
                Debug.LogError("AddressableAsset runtime key not valid.  Item: " + newItem.itemID);
                return false;
            }
            else
            {
                return true;
            }
        }

        // This method can either get called from adding an item to automation if you let it send a the SystemChanged event,
        // or from being already registered in the culling system and your sphere coming on screen
        // (the event is not used when items are spawned on screen and then added to the system, in that case the item registers also to culling itself)
        // (the event is not used when items are loaded from save, because that happens before the camera is in place and the culling events should happen normally after)
        // (the event seems to be used to load blueprints)
        private void TryGetPooledDisplayPrefab(Item item, AutomationGridSpace space, Transform container, AddedObjectType objectType)
        {
            bool itemIsPooled = _pooledItemFactory.IsItemPooled(item);

            if (itemIsPooled)
            {
                // Items register in automation and display, then destroy themseves; then we spawn a new item display if/when the item is visible
                // Those items register in OnDataLoadComplete. Pool needs a moment to spin up, though.
                // We need to make sure the pool is initialized before we grab a core item from it, otherwise it will just have an error.
                this.WaitForConditionIfNeeded(() => GameState.PoolsInitialized && GameState.GameDataLoaded,
                                              () =>
                                              {
                                                  // Delay the whole logic until pool is ready, even for non-pooled items?
                                                  GameObject obtainedItem = _pooledItemFactory.GetPooledItem(item);
                                                  if (obtainedItem != null)
                                                  {
                                                      obtainedItem.transform.position = space.position;
                                                      obtainedItem.transform.parent = container;
                                                      ReadyNewDisplayPrefab(obtainedItem, space, objectType);
                                                  }
                                                  // This logic will be removed from here once we improve how we detect pooling support
                                                  else
                                                  {
                                                      CreateDisplayPrefabAsync(item.addressableAsset, space, container, objectType);
                                                  }
                                              });
            }
            else
            {
                CreateDisplayPrefabAsync(item.addressableAsset, space, container, objectType);
            }
        }

        private void CreateDisplayPrefabAsync(AssetReference asset, AutomationGridSpace space, Transform container, AddedObjectType objectType)
        {
            if (asset.RuntimeKeyIsValid())
            {
                asset.InstantiateAsync(space.position, Quaternion.identity, container).Completed += op =>
                {
                    if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                    {
                        GameObject createdObject = op.Result;

                        if (createdObject == null)
                        {
                            Debug.LogError($"Null GameObject returned from CreateDisplayPrefabAsync call. Asset Guid:{asset.AssetGUID}, Key:{asset.Asset.name}.");
                            return;
                        }

                        ReadyNewDisplayPrefab(createdObject, space, objectType);
                    }
                };
            }
            else
            {
                Debug.LogError("Runtime key invalid for asset: " + asset);
            }
        }

        private void ReadyNewDisplayPrefab(GameObject createdObject, AutomationGridSpace space, AddedObjectType objectType)
        {
            space.DisplayItem = createdObject;

            if (createdObject.TryGetComponent(out ISpecialPositioning positioning))
                positioning.SetupWorldPosition(space);

            // Setting active first so Awake and Start methods are called before SetGridSpace in case some initialization needs to happen
            createdObject.SetActive(true);

            // This configuration is sensitive to the item's real position being accurate
            IAutomationGridSpaceDisplay[] displays = createdObject.GetComponents<IAutomationGridSpaceDisplay>();
            for (int i = 0; i < displays.Length; i++)
            {
                displays[i].SetGridSpace(space);
            }

            // The variable Visible is a bad name from before some refactoring, but it's the exact same thing as the VisualCulling one.
            if ((objectType == AddedObjectType.ItemProcessor && space.itemProcessor.Visible)
            || (objectType == AddedObjectType.Ressource && space.Resource.VisualCulling))
            {
                // Some items are serialized as Visible but they should not have been.
                // There is a lot of bagage that comes with doing proper upgrade code to fix this, so for now just have them as an edge case.
                bool ignoreTheFlag = NeedsVisualFlagFixHack(space.itemProcessor?.ProcessorID);

                if (!ignoreTheFlag)
                {
                    // This method internally compensates the other way for the offset we just fixed previously, if appropriate. Doing
                    // this after SetGridSpace to make sure direction is set as it can impact the ISpecialPositioning logic when figuring
                    // out which culling group to add this to
                    _cullingController.AddStaticDisplayObject(createdObject);
                }
            }
        }

        public void AddObjectToAreaDictionary(GameObject createdObject)
        {
            _cullingController.AddStaticDisplayObject(createdObject);
            AutomationGridSpace space = _system.GetOrCreateGridSpace(createdObject.transform.position);
            space.DisplayItem = createdObject;
        }

        public void RemoveObjectFromAreaDictionary(GameObject objectToRemove)
        {
            _cullingController.ReleaseStaticDisplayObject(objectToRemove);
            if (_system.TryGetExistingGridSpace(objectToRemove.transform.position, out AutomationGridSpace space))
            {
                if (space.DisplayItem == objectToRemove)
                {
                    space.DisplayItem = null;
                }
            }
        }

        public void AddObjectToMovingItems(GameObject displayObject, AutomationCoreItem coreItem)
        {
            int cullingIndex = GetNextMovingItemIndexAndIncrement();

            _movingItemToIndexMap.Add(coreItem, cullingIndex);

            // Track the item in the moving spheres culling group
            if (!_movingItems.ContainsKey(cullingIndex))
            {
                _movingItems.Add(cullingIndex, coreItem);
            }
            else
            {
                if (_movingItems[cullingIndex] != coreItem)
                    Debug.LogWarning($"Index {cullingIndex} already exists in MovingItems dictionary. Existing item {_movingItems[cullingIndex].ID}.  Total moving items: {_movingItems.Count}");
            }

            _movingSpheres[cullingIndex].position = coreItem.GridSpace != null ? coreItem.GridSpace.position : coreItem.position;
            _movingSpheres[cullingIndex].radius = 10f;

            _movingDisplayObjects[cullingIndex] = displayObject;
        }

        public void RemoveObjectFromMovingItems(AutomationCoreItem coreItem)
        {
            if (_movingItemToIndexMap.TryGetValue(coreItem, out int sphereIndex))
            {
                _movingItems.Remove(sphereIndex);
                _movingItemToIndexMap.Remove(coreItem);

                if (_movingDisplayObjects.TryGetValue(sphereIndex, out GameObject displayObject))
                {
#if DISPLAY_SYSTEM_LOGGING
                    Debug.Log("Deleting object at: " + displayObject.transform.position);
#endif
                    PoolableObject pooled = displayObject.GetComponent<PoolableObject>();
                    if (pooled != null)
                    {
                        _pooledItemFactory.ReturnPooledCoreItem(pooled);
                    }
                    else
                    {
                        // Moving items should only be moving around as pure core item form (like dropped inventory) so should all be pooled
                        Debug.LogError($"AutomationSystemDisplay.RemoveObjectFromMovingItems unexpectedly sees that {displayObject.name} is not poolable and releases it.");
                        Addressables.ReleaseInstance(displayObject);
                    }

                    _movingDisplayObjects.Remove(sphereIndex);
                }

                // Add index back into possible index queue
                _movingItemIndexes.Enqueue(sphereIndex);

#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"CoreItem removed. Index:{sphereIndex}");
#endif
            }
        }

        public void CreateItemDisplayPrefab(AutomationCoreItem item, Vector3 position, bool autoCollect, bool bounceOnSpawn = false)
        {
            bool spaceOnScreen = _cullingController.IsSpaceVisible(item.position);

            int cullingIndex = GetNextMovingItemIndexAndIncrement();

            _movingItemToIndexMap.Add(item, cullingIndex);

            if (spaceOnScreen)
            {
                CreateItemDisplayPrefabAsync(item, cullingIndex, item.position, _coreItemContainer, false, false);
            }
            // If off screen track in moving items dictionary.  If on screen this is done once asnyc object creation is completed
            else
            {
                if (_movingItems.ContainsKey(cullingIndex))
                {
                    Debug.LogError($"Item at index {cullingIndex} exists already.  Item: {_movingItems[cullingIndex].ID}");
                    return;
                }

                // Track the item in the moving spheres culling group
                _movingItems.Add(cullingIndex, item);
                _movingSpheres[cullingIndex].position = item.position;
                _movingSpheres[cullingIndex].radius = _movingItemSphereRadius;

#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"CoreItem added. OnScreen:{spaceOnScreen}. Index:{cullingIndex}. MovingItems: {_movingItems.Count}, MovingItemToIndexMap: {_movingItemToIndexMap.Count}");
#endif
            }
        }

        private void CreateItemDisplayPrefabAsync(AutomationCoreItem item, int cullingIndex, Vector3 position, Transform container, bool autoCollect, bool bounceOnSpawn = true)
        {
#if DISPLAY_SYSTEM_LOGGING
            Debug.Log("Creating Display prefab for " + item.ID);
#endif
            Vector3 spawnPosition = position;

            // If no processor on space, add a bit of randomness to position to items aren't directly stacked on top of each other in game
            if (_system.GetOrCreateGridSpace(position).itemProcessor == null)
                spawnPosition += UnityUtils.RandomXZVector(0.35f);

            if (item.DisplayAssetOverride != null && item.DisplayAssetOverride.RuntimeKeyIsValid())
            {
                // Overrides are not pooled at the moment. (AFAIK only override we use is for the deer trap)
                item.DisplayAssetOverride.InstantiateAsync(spawnPosition, Quaternion.identity, container).Completed += op =>
                {
                    if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                    {
                        GameObject createdObject = op.Result;

                        // Because the creation of the visual asset is async, it is possible that the automation system
                        // has consumed it before it got time to appear (for instance by being pushed into a scrap chest)
                        if (item == null || item.IsConsumed)
                        {
                            GameObject.Destroy(createdObject);
                            return;
                        }

                        InitializeCoreItemInstance(createdObject, item, cullingIndex, position, autoCollect, bounceOnSpawn);
                    }
                };
            }
            else
            {
                // Items register in automation and display, then destroy themseves; then we spawn a new item display if/when the item is visible
                // Those items register in OnDataLoadComplete. Pool needs a moment to spin up, though.
                // We need to make sure the pool is initialized before we grab a core item from it, otherwise it will just have an error.
                this.WaitForConditionIfNeeded(() => GameState.GameDataLoaded,
                                              () =>
                                              {
                                                  GameObject obtainedObject = _pooledItemFactory.GetPooledCoreItem();
                                                  obtainedObject.transform.position = spawnPosition;
                                                  obtainedObject.transform.rotation = Quaternion.identity;
                                                  obtainedObject.transform.parent = container;
                                                  InitializeCoreItemInstance(obtainedObject, item, cullingIndex, position, autoCollect, bounceOnSpawn);
                                              });
            }
        }

        private void InitializeCoreItemInstance(GameObject createdObject, AutomationCoreItem item, int cullingIndex, Vector3 position, bool autoCollect, bool bounceOnSpawn)
        {
            // If the item was pooled, it might come to us inactive, so let's activate it now first, ensuring awake calls are resolved
            createdObject.SetActive(true);

            // Track the item in the moving spheres culling group
            if (!_movingItems.ContainsKey(cullingIndex))
            {
                _movingItems.Add(cullingIndex, item);
            }
            else
            {
                if (_movingItems[cullingIndex] != item)
                    Debug.LogWarning($"Index {cullingIndex} already exists in MovingItems dictionary. Existing item {_movingItems[cullingIndex].ID}.  Total moving items: {_movingItems.Count}");
            }

            _movingSpheres[cullingIndex].position = position;
            _movingSpheres[cullingIndex].radius = 10f;

            _movingDisplayObjects[cullingIndex] = createdObject;

            if ((bounceOnSpawn || autoCollect) && createdObject.TryGetComponent(out ItemSpawnBehaviour spawnComponent))
            {
                if (!bounceOnSpawn)
                    spawnComponent.SetStartForce(0f);

                spawnComponent.StartItemBounce(autoCollect);
            }

            IAutomationItemDisplay[] displays = createdObject.GetComponents<IAutomationItemDisplay>();

            for (int i = 0; i < displays.Length; i++)
            {
                displays[i].SetItem(item, _system);
            }

            MovingItemDisplayObjectCreated?.Invoke(this, new AutomationDisplayChangedEvent(AutomationDisplayChangedEvent.ChangeType.AddMovingItem, createdObject, item));

#if DISPLAY_SYSTEM_LOGGING
            Debug.Log($"CoreItem added. OnScreen:True. Index:{cullingIndex}. MovingItems: {_movingItems.Count}, MovingItemToIndexMap: {_movingItemToIndexMap.Count}");
#endif
        }

        private int GetIndexForNearestBoundingSphere(Vector3 vector)
        {
            Vector3 snappedPosition = vector.GetSnappedPosition(_cullingAreaGridDiameter);

            Vector3 sphereRelativePosition = (snappedPosition - _bottomCorner) / _cullingAreaGridDiameter;

            return (int)(sphereRelativePosition.x * _cullingSpheresPerRow + sphereRelativePosition.z);
        }

        private void CullingGroupStateChanged(CullingGroupEvent evt)
        {
            if (!_dataLoadComplete)
                return;

            if (evt.hasBecomeInvisible)
            {
#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"Sphere {evt.index} is now invisible");
#endif
                HideObjectsInArea(evt.index);
            }
            else if (evt.hasBecomeVisible)
            {
#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"Sphere {evt.index} is now visible");
#endif
                int positionCount = _cullingController.GetGridPositionsInBoundingArea(evt.index, ref _gridPositionsBuffer);

                // Need to copy buffer so it isn't overwritten while running coroutine to create the objects
                Vector3[] positions = (Vector3[])_gridPositionsBuffer.Clone();
#if DISPLAY_SYSTEM_LOGGING
                Debug.Log($"Sphere: {evt.index}. Showing Objects from {positions[0]} to {positions[positionCount - 1]}");
#endif
                StartCoroutine(ShowObjectsInArea(positions, positionCount));
            }
        }

        private void HideObjectsInArea(int sphereIndex)
        {
            if (_runningShowObjectsRoutines.TryGetValue(sphereIndex, out Coroutine routine))
            {
                if (routine != null)
                {
#if DISPLAY_SYSTEM_LOGGING
                    Debug.Log($"{sphereIndex} Cancelling routine");
#endif
                    StopCoroutine(routine);
                }

                _runningShowObjectsRoutines.Remove(sphereIndex);
            }

            _cullingController.ReleaseObjectsInBoundingArea(sphereIndex, _system);
        }

        private IEnumerator ShowObjectsInArea(Vector3[] gridPositions, int count)
        {
            for (int i = 0; i < count; i++)
            {
                if (_system.DoesSpaceExist(gridPositions[i]))
                {
                    AutomationGridSpace space = _system.GetOrCreateGridSpace(gridPositions[i]);

                    CreateDisplayObjectsForSpace(space);

                    //Every second iteration, yield for a frame
                    if (i % 4 == 0)
                    {
                        yield return null;
                    }
                }
            }
        }

        private void CreateDisplayObjectsForSpace(AutomationGridSpace space)
        {
            // Split the previous IsRendered flag into separate processor and resource ones, in case a resource is created during load before the processor is actually visible, blocking the display of the processor
            // Happens on Planter because both exist on the space at the same time

            if (!space.ProcessorRendered && space.itemProcessor != null && space.itemProcessor.Visible && _masterItemList.TryGetItemByID(space.itemProcessor.ProcessorID, out AdvancedItem newItem))
            {
                TryGetPooledDisplayPrefab(newItem, space, _processorsContainer, AddedObjectType.ItemProcessor);

                space.ProcessorRendered = true;
            }

            if (!space.ResourceRendered && space.Resource != null && space.Resource.VisualCulling && _masterItemList.TryGetItemByID(space.Resource.ID, out HarvestableItem harvestItem))
            {
                if (harvestItem.addressableAsset.RuntimeKeyIsValid())
                {
                    TryGetPooledDisplayPrefab(harvestItem, space, _resourcesContainer, AddedObjectType.Ressource);
                }
                else
                {
                    Debug.LogError($"Cannot create asset for {harvestItem.itemID}. RuntimeKey not valid");
                }

                space.ResourceRendered = true;
            }
        }

        // Moving Items Culling Functionality
        public event EventHandler<AutomationDisplayChangedEvent> MovingItemDisplayObjectCreated;

        [Header("Moving Items Culling")]
        [SerializeField] private int _maxMovingItemCount = 30;
        [SerializeField] private float _movingItemSphereRadius = 6f;

        private Dictionary<int, GameObject> _movingDisplayObjects;
        private Dictionary<int, AutomationCoreItem> _movingItems;
        private Dictionary<AutomationCoreItem, int> _movingItemToIndexMap;
        private CullingGroup _movingItemsCullingGroup;
        private BoundingSphere[] _movingSpheres;
        private Queue<int> _movingItemIndexes = new Queue<int>();

        private void InitializeMovingCullingGroup()
        {
            _movingDisplayObjects = new Dictionary<int, GameObject>(_maxMovingItemCount);
            _movingItems = new Dictionary<int, AutomationCoreItem>(_maxMovingItemCount);
            _movingItemToIndexMap = new Dictionary<AutomationCoreItem, int>(_maxMovingItemCount);
            _movingSpheres = new BoundingSphere[_maxMovingItemCount];

            _movingItemIndexes = new Queue<int>(_maxMovingItemCount);

            for (int i = 0; i < _maxMovingItemCount; i++)
            {
                _movingItemIndexes.Enqueue(i);
            }

            _movingItemsCullingGroup = new CullingGroup();
            _movingItemsCullingGroup.targetCamera = Camera.main;

            _movingItemsCullingGroup.SetBoundingSpheres(_movingSpheres);
            _movingItemsCullingGroup.SetBoundingSphereCount(_maxMovingItemCount);

            _movingItemsCullingGroup.onStateChanged = MovingCullingGroupStateChanged;
        }

        private void MovingCullingGroupStateChanged(CullingGroupEvent evt)
        {
            if (evt.hasBecomeVisible && _movingItems.ContainsKey(evt.index))
            {
                AutomationCoreItem item = _movingItems[evt.index];

                if (!_movingDisplayObjects.ContainsKey(evt.index))
                {
                    CreateItemDisplayPrefabAsync(item, evt.index, item.position, _coreItemContainer, false, false);
                }

            }
            else if (evt.hasBecomeInvisible && _movingDisplayObjects.ContainsKey(evt.index))
            {
                GameObject go = _movingDisplayObjects[evt.index];

                if (go.IsNullOrDestroyed())
                {
                    Debug.LogError("It seems we've left a destroyed GameObject in the moving display objects dictionary.");
                }

                PoolableObject pooled = go.GetComponent<PoolableObject>();
                if (pooled != null)
                {
                    _pooledItemFactory.ReturnPooledCoreItem(pooled);
                }
                else
                {
                    // Happens for instance on DeerTraps. (not sure if it happens on any other item)
                    Debug.LogWarning($"AutomationSystemDisplay.MovingCullingGroupStateChanged sees that {go.name} is not poolable and releases it.");
                    Addressables.ReleaseInstance(go);
                }

                _movingDisplayObjects.Remove(evt.index);
            }
        }

        private int GetNextMovingItemIndexAndIncrement()
        {
            if (_movingItemIndexes.Count > 0)
                return _movingItemIndexes.Dequeue();
            else
            {
                Debug.LogWarning("Out of available indexes for moving items.  Increasing capacity");

                int increaseSize = 5000;

                for (int i = 0; i < increaseSize; i++)
                {
                    _movingItemIndexes.Enqueue(_maxMovingItemCount + i);
                }

                BoundingSphere[] expandedArray = new BoundingSphere[_maxMovingItemCount + increaseSize];

                for (int i = 0; i < _movingSpheres.Length; i++)
                {
                    expandedArray[i] = _movingSpheres[i];
                }

                _movingSpheres = expandedArray;
                _movingItemsCullingGroup.SetBoundingSpheres(_movingSpheres);
                _movingItemsCullingGroup.SetBoundingSphereCount(_maxMovingItemCount + increaseSize);

                _maxMovingItemCount += increaseSize;
            }

            return _movingItemIndexes.Dequeue();
        }

        private void UpdateMovingItemsSpherePositions()
        {
            foreach (int itemIndex in _movingItems.Keys)
            {
                _movingSpheres[itemIndex].position = _movingItems[itemIndex].position;
            }
        }

        //public bool TryGetDisplayForItem(out GameObject displayObject, Vector3 position, AutomationCoreItem autoItem)
        //{
        //    if(_movingItemToIndexMap.TryGetValue(autoItem, out int index))
        //    {
        //        if (_movingDisplayObjects.TryGetValue(index, out displayObject))
        //            return true;
        //    }

        //    displayObject = null;

        //    return false;
        //}

        // These are serialized as Visible but they should not have been. Ideally we'd fix the data but for now just ignore them.
        public bool NeedsVisualFlagFixHack(string processorItemID)
        {
            if (string.IsNullOrEmpty(processorItemID))
                return false;

            if (_brokenVisualFlagItemsToFix == null)
                return false;

            for (int i = 0; i < _brokenVisualFlagItemsToFix.Count; i++)
            {
                if (_brokenVisualFlagItemsToFix[i].itemID == processorItemID)
                {
                    return true;
                }
            }

            return false;
        }

        // Gizmos

        [Header("Gizmos")]
        [SerializeField] private bool _showStaticCullingSpheres = true;
        [SerializeField] private bool _showCullingGridBounds = true;
        [SerializeField] private bool _showCullingIndex = false;
        [SerializeField] private Color _staticCullingSphereVisibleColor = default;
        [SerializeField] private Color _staticCullingSphereHIddenColor = default;

        [Space(20)]
        [SerializeField] private bool _showSpaceContents = false;
        [SerializeField] private Color _processorColor = default;
        [SerializeField] private Color _coreItemColor = default;
        [SerializeField] private Color _resourceColor = default;
        [SerializeField] private Color _poweredColor = default;

        [Space(20)]
        [SerializeField] private bool _showMovingItemSpheres = false;
        [SerializeField] private Color _movingItemSphereColor = default;

        private void OnDrawGizmosSelected()
        {
            if (_cullingController == null)
                return;

            DrawCullingSpheres();

            if (_showSpaceContents)
            {
                foreach (AutomationGridSpace space in _system.Grid.ActiveSpaces)
                {
                    ShowSpaceGizmos(space);
                }

                foreach (AutomationGridSpace space in _system.Grid.SleepingSpaces)
                {
                    ShowSpaceGizmos(space);
                }
            }

            if (_showMovingItemSpheres)
            {
                foreach (int index in _movingItems.Keys)
                {
                    Gizmos.color = _movingItemSphereColor;
                    Gizmos.DrawWireSphere(_movingSpheres[index].position, _movingSpheres[index].radius);
                    Gizmos.DrawSphere(_movingSpheres[index].position, 0.5f);
                }
            }

            if (DrawTree)
                DrawGridTree();
        }

        private void ShowSpaceGizmos(AutomationGridSpace space)
        {
            Vector3 spaceCenter = space.position + Constants.GRID_CENTERING_OFFSET;

            if (space.itemProcessor != null)
            {
                Gizmos.color = _processorColor;
                Gizmos.DrawCube(spaceCenter, Vector3.one * 0.8f);

                if (space.itemProcessor is IDirectionalItem direction)
                {
                    Gizmos.DrawRay(spaceCenter, direction.GetDirection());
                }
            }

            if (space.ActiveItem != null)
            {
                Gizmos.color = _coreItemColor;
                Gizmos.DrawSphere(spaceCenter, 0.35f);
            }

            if (space.Resource != null)
            {
                Gizmos.color = _resourceColor;
                Gizmos.DrawSphere(spaceCenter + Vector3.up * 0.15f, 0.35f);
            }

            if (space.Powered)
            {
                Gizmos.color = _poweredColor;
                Gizmos.DrawSphere(spaceCenter - Constants.GRID_CENTERING_OFFSET * 0.5f, 0.15f);
            }

            if (space.IsEmpty())
            {
                Gizmos.color = Color.black;
                Gizmos.DrawCube(spaceCenter, Vector3.one * 0.8f);
            }
        }

        private void DrawCullingSpheres()
        {
            if (_showStaticCullingSpheres || _showCullingGridBounds)
            {
                Vector3[] gridPositionsBuffer = new Vector3[100];

                for (int i = 0; i < _cullingController.Spheres.Length; i++)
                {
                    bool isVisible = _cullingController.CullingGroup.IsVisible(i);

                    Gizmos.color = isVisible ? _staticCullingSphereVisibleColor : _staticCullingSphereHIddenColor;

                    if (_showStaticCullingSpheres)
                        Gizmos.DrawWireSphere(_cullingController.Spheres[i].position, _cullingController.Spheres[i].radius);

                    if (_showCullingGridBounds)
                        Gizmos.DrawWireCube(_cullingController.Spheres[i].position, new Vector3(_cullingController.CullingAreaGridDiameter, 1, _cullingController.CullingAreaGridDiameter));

                    if (_showCullingIndex && isVisible)
                    {
#if UNITY_EDITOR
                        UnityEditor.Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

                        GUIStyle textStyle = new GUIStyle();
                        textStyle.normal.textColor = _staticCullingSphereVisibleColor;
                        UnityEditor.Handles.Label(_cullingController.Spheres[i].position + Vector3.up * 0.5f, i.ToString(), textStyle);
#endif
                    }
                }
            }

        }

        public bool DrawTree = false;
        public Color gridTreeColor = Color.white;

        private void DrawGridTree()
        {
            Gizmos.color = gridTreeColor;

            DrawQuad(_system.Tree);

            Gizmos.color = Color.cyan;
            Vector3 center = new Vector3((topLeftSearch.x + botRightSearch.x) / 2f, 0f, (topLeftSearch.z + botRightSearch.z) / 2f);
            Gizmos.DrawWireCube(center, new Vector3(botRightSearch.x - topLeftSearch.x, 0.1f, topLeftSearch.z - botRightSearch.z));
        }

        private void DrawQuad(AutomationQuad quad)
        {
            if (quad == null)
                return;

            Vector3 center = new Vector3((quad.topLeftX + quad.botRightX) / 2f, 0f, (quad.topLeftZ + quad.botRightZ) / 2f);
            Gizmos.DrawWireCube(center, new Vector3(quad.botRightX - quad.topLeftX, 0.1f, quad.topLeftZ - quad.botRightZ));

            if (quad.node != null)
            {
                Gizmos.DrawSphere(quad.node.position, 0.4f);
            }

            DrawQuad(quad.topLeftTree);
            DrawQuad(quad.topRightTree);
            DrawQuad(quad.botLeftTree);
            DrawQuad(quad.botRightTree);
        }

        public Vector3Int topLeftSearch;
        public Vector3Int botRightSearch;

        [ContextMenu("Search Area")]
        public void SearchArea()
        {
            List<QuadTreeNode> nodes = _system.Tree.Search(topLeftSearch, botRightSearch);

            for (int i = 0; i < nodes.Count; i++)
            {
                Debug.Log($"Node {i}: {nodes[i].position}");
            }

            Debug.Log("Total Nodes: " + nodes.Count);
        }

        public void OnDataLoadComplete()
        {
            _dataLoadComplete = true;

            RefreshVisibleItems();
        }
    }

    public class AutomationDisplayChangedEvent : EventArgs
    {
        public enum ChangeType { AddMovingItem }

        public ChangeType type;
        public GameObject displayObject;
        public AutomationCoreItem automationItem;

        public AutomationDisplayChangedEvent(ChangeType type, GameObject displayObject, AutomationCoreItem automationItem)
        {
            this.type = type;
            this.displayObject = displayObject;
            this.automationItem = automationItem;
        }
    }
}