// Copyright Isto Inc.
using Isto.Core.Data;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;

namespace Isto.Core.BugReportingTool
{
    public class JiraConnection
    {
        public event EventHandler<string> SubmissionResult;
        private JiraUser _currentJiraUser;
        private Dictionary<string, string> _mimeTypeMap;

        private static readonly string MAIN_URL = "https://istoinc.atlassian.net/";
        private static readonly string SUBMISSION_URL = "rest/api/2/issue";
        private static readonly string EMPTY_JSON_STRING = "{}";
        static readonly string BASIC_AUTHENTICATION_PREFIX = "Basic ";

        public JiraConnection(JiraUser jiraUser)
        {
            _currentJiraUser = jiraUser;
            SetupMimeTypeDictionary();
        }

        private void SetupMimeTypeDictionary()
        {
            // This can be expanded upon if we need more types for Jira
            _mimeTypeMap = new Dictionary<string, string>
            {
                { ".png", "image/png" },
                { ".txt", "text/plain" },
                { ".log", "text/plain" },
                { ".zip", "application/zip" }
            };
        }
        
        public IEnumerator ProcessRequest(JiraBugData jiraBugData)
        {
            string fullURL = MAIN_URL + SUBMISSION_URL;

            JiraJsonContainer jiraJsonContainer = JiraBugTypeConverter.JiraBugDataToJiraJsonContainer(jiraBugData, _currentJiraUser);

            string jiraBugJson = JsonUtility.ToJson(jiraJsonContainer);

            using (UnityWebRequest request = UnityWebRequest.Get(fullURL))
            {
                string authorization = GetAuthenticationString(_currentJiraUser.Email, _currentJiraUser.JiraApiToken);

                // Set header information
                request.method = UnityWebRequest.kHttpVerbPOST;
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Accept", "application/json");
                request.SetRequestHeader("AUTHORIZATION", authorization);

                byte[] encodedText = System.Text.Encoding.UTF8.GetBytes(jiraBugJson);
                request.uploadHandler = new UploadHandlerRaw(encodedText);

                yield return request.SendWebRequest();

                DownloadHandler downloadHandler = request.downloadHandler;

                if (request.result != UnityWebRequest.Result.Success)
                {
                    string errorMessage = $"Unity Web Request Failed. Reason: {request.result}";
                    SubmissionResult?.Invoke(this, errorMessage);
                    Debug.LogError(errorMessage);
                }
                else if (downloadHandler.text == EMPTY_JSON_STRING || string.IsNullOrEmpty(downloadHandler.text))
                {
                    string errorMessage = "The web request has returned an empty response.";
                    SubmissionResult?.Invoke(this, errorMessage);
                    Debug.LogError(errorMessage);
                }
                else
                {
                    // Jira upload was a success
                    JiraJsonResult jiraResultData = JsonUtility.FromJson<JiraJsonResult>(downloadHandler.text);
                    string issueNumber = jiraResultData.key;
                    Debug.Log($"Unity Web Request Succeeded! Issue number {issueNumber} created.");
                    
                    SubmissionResult?.Invoke(this, "Success!");

                    yield return UploadFileToJiraIssue(issueNumber, jiraBugData.ImageFilePath);
                    yield return AttachPlayerLog(issueNumber);
                }
            }
        }

        private IEnumerator AttachPlayerLog(string issueNumber)
        {
            string playerLogFilePath = Path.Combine(Application.persistentDataPath, "Player.log");
            yield return UploadFileToJiraIssue(issueNumber, playerLogFilePath);
        }

        private IEnumerator UploadFileToJiraIssue(string issueNumber, string filePath)
        {
            string fullURL = MAIN_URL + SUBMISSION_URL;
            string fileName = Path.GetFileName(filePath);
            string mimeType = GetMimeTypeFromFile(filePath);

            // Set Attachent
            byte[] fileBytes = File.ReadAllBytes(filePath);
            WWWForm form = new WWWForm();
            form.AddBinaryData("file", fileBytes, fileName, mimeType);
            UnityWebRequest webRequest = UnityWebRequest.Post(fullURL + "/" + issueNumber + "/attachments", form);

            // Set header information
            webRequest.method = UnityWebRequest.kHttpVerbPOST;
            webRequest.SetRequestHeader("X-Atlassian-Token", "no-check");
            string authorization = GetAuthenticationString(_currentJiraUser.Email, _currentJiraUser.JiraApiToken);
            webRequest.SetRequestHeader("AUTHORIZATION", authorization);

            yield return webRequest.SendWebRequest();

            if (webRequest.result != UnityWebRequest.Result.Success)
            {
                Debug.LogError($"Unity Web Request Failed. Reason: {webRequest.result}");
            }
            else if (webRequest.downloadHandler.text == "{}" || string.IsNullOrEmpty(webRequest.downloadHandler.text))
            {
                Debug.LogError("The web request has returned and empty response.");
            }
            else
            {
                // Jira image attachment was a success
                Debug.Log($"Unity Web Request Succeeded! File {filePath} successfully attached.");
            }
        }

        string GetMimeTypeFromFile(string filePath)
        {
            string fileExtension = Path.GetExtension(filePath);
            if (!_mimeTypeMap.TryGetValue(fileExtension, out string mimeType))
            {
                Debug.LogError($"MimeType could not be found for extension {fileExtension}. Please ensure that it " +
                               $"has been added to the dictionary.");
            }
            return mimeType;
        }

        string GetAuthenticationString(string username, string password)
        {
            string usernameWithPassword = username.Trim() + ":" + password.Trim();
            Encoding isoEncoding = Encoding.GetEncoding("ISO-8859-1");
            byte[] encodedBytes = isoEncoding.GetBytes(usernameWithPassword);
            string authenticationString = Convert.ToBase64String(encodedBytes);

            authenticationString = BASIC_AUTHENTICATION_PREFIX + authenticationString;
            return authenticationString;
        }
    }
}