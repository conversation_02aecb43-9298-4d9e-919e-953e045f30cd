// Copyright Isto Inc.
using I2.Loc;
using Isto.Core.Analytics;
using Isto.Core.Automation;
using Isto.Core.Configuration;
using Isto.Core.Data;
using Isto.Core.Localization;
using Isto.Core.Platforms;
using Isto.Core.Scenes;
using Isto.Core.Themes;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;
using Zenject;

namespace Isto.Core.Game
{
    /// <summary>
    /// Class to track progress made by player and other Gameplay related data.
    /// Also controls setting the correct control mode on scene loading for input
    /// </summary>
    public class GameState : MonoBehaviour
    {
        // UNITY HOOKUP

        public TimeStepSetup timeStepSetup;
        public SceneSettingMappings settingsMapping;
        public VersionInfo version;
        public LocalizedString defaultSlotNameKey;


        // OTHER FIELDS

        protected int _saveSlot;

        protected string _currentEssentialScene;
        protected string _currentStartingAreaScene;

        protected List<GameObject> _rootObjectsBuffer = new List<GameObject>();
        protected List<IDataLoadCompleteHandler> _loadCompleteListenersBuffer = new List<IDataLoadCompleteHandler>();

        // This is assigned during Preloading of the game state data for selected save slot
        protected GameStateSaveData _selectedSaveSlotMetaData;

        private int _loadedGameSlotNumber;

        private GameStateDataManager _gameStateDataManager;
        private GameModeDefinition _currentGameMode;

        private bool _mainSceneLoaded = false;
        private bool _essentialSceneActive = false;


        // PROPERTIES

        public static bool CurrentlyLoading { get; private set; }
        public static float LoadProgress { get; private set; }
        public static string LoadingStep { get; private set; }
        public static bool LoadingFromSave { get; private set; }
        // Initialization sequencing and blocking is not easy to introduce elegantly so for now I will just add some static gating booleans.
        // When we have too many of them or we need to introduce a better option, we can take the whole thing into account and refactor these.
        public static bool WaitingForSaveFileLoad { get; set; }
        public static bool PoolsInitialized { get; set; } = true; // Pools block creation of pooled item
        public static bool AutomationSystemCanStart { get; set; }
        public static bool GameDataLoaded { get; set; } // This signifies that our OnDataLoadComplete callback has been sent
        public static bool StoryAnimationActive { get; set; }

        // Is Set to true when a game is started from StartGameRoutine(). This allows to detect if the game was started
        // from the title menu in a natural way or if started directly in the scene from the editor.
        public static bool GameModeLoadedFromGameState { get; private set; }

        public GameStateSaveData CurrentSaveMetaData
        {
            get
            {
                return _selectedSaveSlotMetaData;
            }
        }

        public GameModeDefinition CurrentGameMode
        {
            get
            {
                return _currentGameMode;
            }
            set
            {
                _currentGameMode = value;

                if (value != null)
                {
                    _analytics?.RegisterGameModeSelected(value);
                }
            }
        }

        public IGameProgressProvider GameProgressProvider { get; set; }

        // EVENTS

        public event EventHandler LoadComplete;


        // INJECTION

        private IGameData _gameData;
        private IAnalyticsHandler _analytics;
        protected ThemeManager _themeManager;
        protected GameScenesReference _coreGameScenesReference;

        [Inject]
        public void Inject(IGameData gameData, IAnalyticsHandler analytics, GameScenesReference gameScenesReference,
                    ThemeManager themeManager)
        {
            _gameData = gameData;
            _analytics = analytics;
            _coreGameScenesReference = gameScenesReference;
            _themeManager = themeManager;
        }


        // LIFECYCLE EVENTS

        public virtual void Awake()
        {
            _currentEssentialScene = _coreGameScenesReference.EssentialsScene;
            _currentStartingAreaScene = _coreGameScenesReference.StartingAreaScene;

            _gameStateDataManager = GetComponent<GameStateDataManager>();
            Debug.Assert(_gameStateDataManager != null, "GameStateDataManager not found on GameState prefab", this);

            TimeStepSetup.PlatformSpecificSettings setup = timeStepSetup.settings.Where((x) => x.platform == Application.platform).FirstOrDefault();
            if (setup != null)
            {
                // Even in editor, setting this value at runtime this way will be reverted when you stop the player.
                Time.fixedDeltaTime = setup.fixedTimeStep;
            }
        }

        public void OnEnable()
        {
            RegisterEvents();
        }

        public void OnDisable()
        {
            UnregisterEvents();
        }

        // EVENT HANDLING

        private void RegisterEvents()
        {
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.activeSceneChanged += OnSceneChanged;
        }

        private void UnregisterEvents()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.activeSceneChanged -= OnSceneChanged;
        }

        private void OnSceneLoaded(Scene newScene, LoadSceneMode arg1)
        {
            int sceneCount = SceneManager.sceneCount;

            bool isStartingAreaScene = IsStartingAreaSceneLoaded(newScene);

            if (isStartingAreaScene)
            {
                _mainSceneLoaded = true;

                if (_essentialSceneActive)
                {
                    LoadData();
                }
            }
        }

        private void OnSceneChanged(Scene oldScene, Scene newScene)
        {
            bool isEssentialScene = IsEssentialScene(newScene);

            if (isEssentialScene)
            {
                _essentialSceneActive = true;

                if (_mainSceneLoaded)
                {
                    LoadData();
                }
            }
        }

        // OTHER METHODS

        private bool IsStartingAreaSceneLoaded(Scene newScene)
        {
            if (!newScene.isLoaded)
                return false;

            return SceneHasParentContract(newScene);
        }

        protected virtual bool IsEssentialScene(Scene newScene)
        {
            return newScene.name == _coreGameScenesReference.EssentialsScene;
        }

        private bool SceneHasParentContract(Scene scene)
        {
            scene.GetRootGameObjects(_rootObjectsBuffer);

            for (int i = 0; i < _rootObjectsBuffer.Count; i++)
            {
                if (_rootObjectsBuffer[i].TryGetComponent(out SceneContext context))
                {
                    if (context.ParentContractNames.Count() > 0)
                        return true;
                }
            }

            return false;
        }

        private void LoadData()
        {
            // Reset flags
            _mainSceneLoaded = false;
            _essentialSceneActive = false;

            if (LoadingFromSave)
            {
                _gameData.LoadGameData(_saveSlot, (bool success) =>
                {
                    WaitingForSaveFileLoad = false;
                    if (!success)
                    {
                        // This means there was a problem getting, deserializing or loading the save data.
                        // On xbox this should mean you have nothing. On PC you might have just one data file missing.
                        // Either way we're not waiting for this to keep loading the game, so at best you can play with
                        // some missing stuff, and at worst you will land in an empty game with missing default objects?
                        Debug.LogError($"LoadGameData failed");
                    }
                });
            }
            else
            {
                WaitingForSaveFileLoad = false;
            }

            StartCoroutine(WaitForInitializationAndSendEvents());
        }

        // Using this init logic we regularize some initialization logic, inform some instances about their spot in the flow,
        // and block many systems from executing until the setup is done, also ensuring we unblock those in the right order for
        // them to initialize correctly.
        // Note that we call LoadGameData asynchronously with this (meanwhile, PooledItemFactory awakes in the scene)
        // We are waiting for the pools to be ready first because the instances in them do expect to get the OnDataLoadComplete event
        // In the case of automation items they will all be immediately filled in the system, and their visuals will "cull in" later
        // however all spawning of pooled visual assets will be blocking until the GameDataLoaded flag is turned on from here a bit further in the flow
        // (also note that any non-pooled assets will just go ahead and instantiate without wait)
        private IEnumerator WaitForInitializationAndSendEvents()
        {
            // Making sure some other stuff can have a chance to setup before automation tries to update
            AutomationSystemCanStart = false;

            bool gameSystemsInitialized;
            do
            {
                // Add here any other flag for other systems we want to gate the rest of initialization
                gameSystemsInitialized = PoolsInitialized && !CurrentlyLoading;
                yield return new WaitForSeconds(0.1f);
            }
            while (!gameSystemsInitialized);

            // individual objects rely on OnDataLoadComplete to know it's safe to get started
            // it means the SCENE is done loading
            SendLoadCompleteEvent();

            // this events causes the black screen to transition, starting player upgrades to install, lights to be enabled
            LoadComplete?.Invoke(this, EventArgs.Empty);

            // Arbitrary magic delay that seems to help with the item entrances picking up all items correctly
            yield return new WaitForSeconds(1f);

            // this blocks the display system from trying to acquire items from the pools
            GameDataLoaded = true;

            // Not sure exactly what is the sync issue here but the automation system used to depend on GameDataLoaded and that
            // caused the item entrances to not detect any item already on them during loading. It seems that starting ticking
            // a frame later mostly gives them the chance they need to pick them up without the need for additional logic.
            yield return null;

            AutomationSystemCanStart = true;
        }

        /// <summary>
        /// Finds all gameobjects in loaded scenes that implement the IDataLoadCompleteHandler interface and calls the
        /// load complete method on them
        /// </summary>
        private void SendLoadCompleteEvent()
        {
            int loadedSceneCount = SceneManager.sceneCount;

            for (int i = 0; i < loadedSceneCount; i++)
            {
                Scene nextScene = SceneManager.GetSceneAt(i);

                nextScene.GetRootGameObjects(_rootObjectsBuffer);

                int rootCount = _rootObjectsBuffer.Count;

                for (int j = 0; j < rootCount; j++)
                {
                    // Note: Not sure why we include inactive objects here. It does mean we reach objects waiting in the pools.
                    // It does also mean we reach some unused-looking objects left in the scene. Maybe they should be cleaned up.
                    _rootObjectsBuffer[j].GetComponentsInChildren(true, _loadCompleteListenersBuffer);

                    int listenersCount = _loadCompleteListenersBuffer.Count;

                    for (int k = 0; k < listenersCount; k++)
                    {
                        _loadCompleteListenersBuffer[k].OnDataLoadComplete();
                    }
                }
            }

            // Seems like this loop above doesn't catch GOs in the Don't Destroy On Load scope.
            // TODO: fix this hack more intelligently

#if PLATFORM_GAMECORE
            //IDataLoadCompleteHandler gameData = GameObject.FindAnyObjectByType<XBoxGameData>();
#else
            IDataLoadCompleteHandler gameData = GameObject.FindAnyObjectByType<PCGameData>();
            if (gameData != null)
            {
                gameData.OnDataLoadComplete();
            }
#endif

            AutomationSystemDisplay automationSystemDisplay = FindObjectOfType<AutomationSystemDisplay>();

            automationSystemDisplay?.RefreshVisibleItems();
        }

        public void LoadTitleScene()
        {
            SceneManager.LoadScene(_coreGameScenesReference.TitleScreen);
        }

        public bool IsInTitleScene()
        {
            return SceneManager.GetActiveScene().name == _coreGameScenesReference.TitleScreen;
        }

        public bool IsGameModeSceneActiveOrLoading(string sceneName)
        {
            return _currentStartingAreaScene == sceneName;
        }

        public void LoadScene(string sceneName)
        {
            SceneManager.LoadScene(sceneName);
        }

        /// <summary>
        /// Reloads the main scene from saved data without going back to the Intro and Dialog scenes
        /// </summary>
        public void ReloadGame()
        {
            if (!CurrentlyLoading)
            {
                CurrentlyLoading = true;
                LoadProgress = 0f;

                _gameData.HasAnySaveData((hasData) =>
                {
                    StartCoroutine(StartGameRoutine(_coreGameScenesReference.EssentialsScene, _coreGameScenesReference.StartingAreaScene, hasData));
                });
            }
        }

        /// <summary>
        /// Restarts the game from the beginning, without going through the intro scenes
        /// </summary>
        public virtual void RestartGame()
        {
            if (!CurrentlyLoading)
            {
                StartCoroutine(StartGameRoutine(_currentEssentialScene, _currentStartingAreaScene, false));
            }
        }

        public void ExitIntro()
        {
            if (!CurrentlyLoading)
            {
                StartCoroutine(StartGameRoutine(_coreGameScenesReference.EssentialsScene, _coreGameScenesReference.StartingAreaScene, false));
            }
        }

        public void SelectSaveGame(int saveSlot)
        {
            if (!CurrentlyLoading)
            {
                _saveSlot = saveSlot;
            }
        }

        public void LoadSaveGame(int saveSlot)
        {
            if (!CurrentlyLoading)
            {
                // Reset time scale just in case
                Time.timeScale = 1.0f;

                _saveSlot = saveSlot;

                // Setting the flag now because the HasSaveData check will need to skip a frame and I don't want the button to double start the load
                // Eventually I should instead wait for the data as part of the coroutine
                CurrentlyLoading = true;
                LoadProgress = 0f;

                _gameData.HasSaveData(saveSlot, (has) =>
                {
                    // This is default scene to load but game mode specific scene might override it during load flow
                    StartCoroutine(StartGameRoutine(_coreGameScenesReference.EssentialsScene, _coreGameScenesReference.StartingAreaScene, has));
                });
            }
        }

        public void StartGameMode(GameModeDefinition gameMode)
        {
            Debug.Assert(gameMode != null, $"Game Mode parameter cannot be null to start a game mode");

            if (CurrentlyLoading)
                return;

            CurrentGameMode = gameMode;

            Time.timeScale = 1;

            if (CurrentGameMode.ShowIntroCutscene && StartIntroScene())
            {
                return;
            }

            if (!settingsMapping.TryGetScene(CurrentGameMode, out SceneSettingMappings.Mappping sceneSetting))
            {
                Debug.LogWarning($"Game Mode {CurrentGameMode.InternalName} not mapped to a scene... defaulting to scene \"{sceneSetting.sceneName}\".");
            }

            StartCoroutine(StartGameRoutine(sceneSetting.parentSceneName, sceneSetting.sceneName, false));
        }

        public virtual bool StartIntroScene()
        {
            return false;
        }

        public void LoadSaveSlotMetaData(int saveSlot, Action<bool> onLoaded)
        {
            // We load the game state settings before loading the scenes
            _gameStateDataManager.PreLoad(saveSlot, (gameStateData) =>
            {
                LoadGameStateData(gameStateData, saveSlot);
                onLoaded?.Invoke(gameStateData != null);
            });
        }

        public void UpdateSaveSlotMetaData(int saveSlot, GameStateSaveData updatedData, Action<bool> onSaved)
        {
            _gameStateDataManager.PreSave(saveSlot, updatedData, onSaved);
        }

        public IEnumerator StartGameRoutine(string essentialSceneName, string startingAreaSceneName, bool loadFromSaveData)
        {
            GameModeLoadedFromGameState = true;
            GameDataLoaded = false;
            CurrentlyLoading = true;
            LoadProgress = 0.05f; // 5% PROGRESS - maybe start above 0 just so bar is a bit visible
            LoadingStep = "1.Loading loading scene";
            WaitingForSaveFileLoad = true; // have to be setup for waiting by default just in case, we'll turn it off later if not needed

            _mainSceneLoaded = false;
            _essentialSceneActive = false;

            _currentEssentialScene = essentialSceneName;
            _currentStartingAreaScene = startingAreaSceneName;

            LoadingFromSave = loadFromSaveData;

            // Get Loading scene.  Scene will be automatically switched to once loaded.
            AsyncOperation loadingSceneOp = SceneManager.LoadSceneAsync(_coreGameScenesReference.LoadingScreen);

            while (!loadingSceneOp.isDone)
            {
                LoadProgress = 0.05f + (loadingSceneOp.progress * 0.05f); // FROM 5% TO 10%
                yield return null;
            }

            // TODO: these update messages and the weight of the progress at each step should be configurable in a
            // project-specific asset
            LoadProgress = 0.10f;  // 10% PROGRESS DONE
            LoadingStep = "2.Warning: Incoming Game";

            // This wait might be too long for certain games... what is it for?
            float timer = 5f;
            while (timer > 0f)
            {
                float normalizedProgress = (5f - timer) * 0.2f;
                LoadProgress = 0.10f + (normalizedProgress * 0.30f); // FROM 10% TO 40%
                yield return null;
                timer -= Time.deltaTime;
            }

            LoadProgress = 0.40f; // 40% PROGRESS DONE
            LoadingStep = "3.Configuring Game";

            if (LoadingFromSave)
            {
                if (_selectedSaveSlotMetaData == null || string.IsNullOrEmpty(_selectedSaveSlotMetaData.gameModeName) || _selectedSaveSlotMetaData.gameModeName == "---")
                {
                    Debug.LogError("Loading Game State: no game mode name found in GameStateSaveData");
                }
                else
                {
                    var mappings = settingsMapping.settingsMappings.Where(x => x.gameMode.InternalName == _selectedSaveSlotMetaData.gameModeName && x.sandboxUseOnly == false);

                    if (mappings.Count() == 1)
                    {
                        CurrentGameMode = mappings.First().gameMode;
                    }
                    else
                    {
                        Debug.LogError($"Loading Game State: Could not setup game mode {_selectedSaveSlotMetaData.gameModeName} ({mappings.Count()} matching items found in settingsMapping), Setting First Found Mode");
                        CurrentGameMode = mappings.First().gameMode;
                    }
                }

                // If at this point we failed to load CurrentGameMode, sending null to the mappings should give us a useable default value still
                if (!settingsMapping.TryGetScene(CurrentGameMode, out SceneSettingMappings.Mappping sceneSetting))
                {
                    Debug.LogWarning($"Game Mode {CurrentGameMode?.InternalName ?? "(null)"} not mapped to a scene... defaulting to scene \"{sceneSetting.sceneName}\".");
                }

                _currentStartingAreaScene = sceneSetting.sceneName;
                _currentEssentialScene = sceneSetting.parentSceneName;
            }

            // wait 0.5f not sure why
            timer = 0.5f;
            while (timer > 0f)
            {
                float normalizedProgress = (0.5f - timer) * 2f;
                LoadProgress = 0.40f + (normalizedProgress * 0.10f); // FROM 40% TO 50%
                yield return null;
                timer -= Time.deltaTime;
            }

            LoadProgress = 0.50f; // 50% PROGRESS DONE
            LoadingStep = "4.Loading Essential Stuff";

            AsyncOperation loadBlockOp = SceneManager.LoadSceneAsync(_currentEssentialScene, LoadSceneMode.Additive);
            loadBlockOp.allowSceneActivation = false;

            while (loadBlockOp.progress < 0.9f)
            {
                LoadProgress = 0.50f + (loadBlockOp.progress * 0.10f); // FROM 50% TO 60%
                yield return null;
            }

            LoadProgress = 0.60f; // 60% PROGRESS DONE
            LoadingStep = "5.Loading Starting Area";

            AsyncOperation loadMainOp = SceneManager.LoadSceneAsync(_currentStartingAreaScene, LoadSceneMode.Additive);
            loadMainOp.allowSceneActivation = false;

            while (loadMainOp.progress < 0.9f)
            {
                LoadProgress = 0.60f + (loadMainOp.progress * 0.15f); // FROM 60% TO 75% (part 1)
                yield return null;
            }

            loadBlockOp.allowSceneActivation = true;
            loadMainOp.allowSceneActivation = true;

            while (!loadMainOp.isDone)
            {
                LoadProgress = 0.60f + (loadMainOp.progress * 0.15f);  // FROM 60% TO 75% (part 2)
                yield return null;
            }

            LoadProgress = 0.75f; // 75% PROGRESS DONE
            LoadingStep = "6.Loading Level Sections and Cooling Down IK";

            // LoadData() will be called next when the scene loading is done, and initialization flow will resume from there.

            SceneManager.SetActiveScene(SceneManager.GetSceneByName(_currentEssentialScene));

            //Final game scene initialization before hiding the loading screen.
            StartCoroutine(FinalizeLoading());

            float finalizeLoadingOperationProgress;
            do
            {
                finalizeLoadingOperationProgress = GetFinalizeProgress();
                LoadProgress = 0.75f + (finalizeLoadingOperationProgress * 0.15f); // FROM 75% TO 90%
                yield return null;
            }
            while (finalizeLoadingOperationProgress <= 0.99f);

            LoadProgress = 0.90f; // 90% PROGRESS DONE
            LoadingStep = "7.Unloading Loading Screen";

            // Unload loading scene and switch to starting scene
            AsyncOperation unloadOp = SceneManager.UnloadSceneAsync(_coreGameScenesReference.LoadingScreen);

            while (unloadOp.progress < 0.9f)
            {
                LoadProgress = 0.90f + (unloadOp.progress * 0.10f); // FROM 90% TO 100%
                yield return null;
            }

            CurrentlyLoading = false;
            LoadProgress = 1.00f; // 100% PROGRESS DONE
            LoadingStep = "8.Begin Happy Fun Time";
        }

        /// <summary>
        /// Use this to process any extra steps required in your project.
        /// This will block further game load logic until you are ready to proceed by
        /// reporting a FinalizeProgress of 1.
        /// </summary>
        protected virtual IEnumerator FinalizeLoading()
        {
            yield return null;
        }

        /// <summary>
        /// Gives the progress of the steps processed within FinalizeLoading if any.
        /// </summary>
        /// <returns>The normalized progress of the finalization.</returns>
        protected virtual float GetFinalizeProgress()
        {
            return 1f;
        }

        public void LoadGameStateData(GameStateSaveData data, int saveSlot)
        {
            _selectedSaveSlotMetaData = data;
            _loadedGameSlotNumber = saveSlot;
        }

        public virtual GameStateSaveData SaveGameStateData()
        {
            GameStateSaveData data = new GameStateSaveData();
            data.isAutoSave = false;
            data.saveSlotName = $"{Loc.Get(defaultSlotNameKey)}{_saveSlot}";
            if (_selectedSaveSlotMetaData != null)
                data.saveSlotName = _selectedSaveSlotMetaData.saveSlotName;
            if (CurrentGameMode != null)
                data.gameModeName = CurrentGameMode.InternalName;
            else
                Debug.LogWarning("Saving Game State: no game mode configured, it will be missing in the save file.");
            data.gameVersion = version.ActiveVersion.VersionText;
            data.internalBuildVersion = version.ActiveVersion.shortDate;

            IGameProgressProvider gameProgress = GameProgressProvider;
            if (gameProgress != null)
            {
                data.totalGameTimeSeconds = gameProgress.GetTotalGameSecondsElapsedInPlaythrough();
            }
            else
            {
                data.totalGameTimeSeconds = 0f;
            }

            data.saveDate = DateTime.Now;
            data.autoSaveDates = new List<DateTime>();
            data.themeData = _themeManager.GetData();

            return data;
        }

        public bool IsCurrentlyLoadedSaveGame(GameStateSaveData data, int saveSlot)
        {
            if (_selectedSaveSlotMetaData == null)
                return false;

            if (_loadedGameSlotNumber == saveSlot && data.saveSlotName == _selectedSaveSlotMetaData.saveSlotName)
                return true;

            return false;
        }

#if UNITY_EDITOR
        // THIS IS JUST FOR TESTING PURPOSES, SAME LOGIC AS OTHER STARTGAMEROUNTINE BUT IS LOADED ADDITIVE SO THE THE TEST SCENE IS NOT UNLOADED
        // Make sure to call SelectSaveSlot before using this so correct files are loaded

        public IEnumerator TestStartGameRoutine(string essentialSceneName, string startingAreaSceneName, bool loadFromSaveData)
        {
            GameDataLoaded = false;
            CurrentlyLoading = true;
            LoadProgress = 0f;

            _mainSceneLoaded = false;
            _essentialSceneActive = false;

            _currentEssentialScene = essentialSceneName;
            _currentStartingAreaScene = startingAreaSceneName;

            LoadingFromSave = loadFromSaveData;

            // Get Loading scene.  Scene will be automatically switched to once loaded.
            AsyncOperation loadingSceneOp = SceneManager.LoadSceneAsync(_coreGameScenesReference.LoadingScreen, LoadSceneMode.Additive);

            while (!loadingSceneOp.isDone)
            {
                yield return null;
            }

            if (LoadingFromSave)
            {
                if (_selectedSaveSlotMetaData == null || string.IsNullOrEmpty(_selectedSaveSlotMetaData.gameModeName))
                {
                    Debug.LogError("Loading Game State: no game mode name found in GameStateSaveData");
                }
                else
                {
                    var mappings = settingsMapping.settingsMappings.Where(x => x.gameMode.InternalName == _selectedSaveSlotMetaData.gameModeName && x.sandboxUseOnly == false);

                    if (mappings.Count() == 1)
                    {
                        CurrentGameMode = mappings.First().gameMode;
                    }
                    else
                    {
                        Debug.LogError($"Loading Game State: Could not setup game mode {_selectedSaveSlotMetaData.gameModeName} ({mappings.Count()} matching items found in settingsMapping), Setting First Found Mode");
                        CurrentGameMode = mappings.First().gameMode;
                    }
                }

                // If at this point we failed to load CurrentGameMode, sending null to the mappings should give us a useable default value still
                if (!settingsMapping.TryGetScene(CurrentGameMode, out SceneSettingMappings.Mappping sceneSetting))
                {
                    Debug.LogWarning($"Game Mode {CurrentGameMode?.InternalName ?? "(null)"} not mapped to a scene... defaulting to scene \"{sceneSetting.sceneName}\".");
                }

                _currentStartingAreaScene = sceneSetting.sceneName;
            }

            yield return new WaitForSeconds(0.5f);

            AsyncOperation loadBlockOp = SceneManager.LoadSceneAsync(_currentEssentialScene, LoadSceneMode.Additive);
            loadBlockOp.allowSceneActivation = false;

            while (loadBlockOp.progress < 0.9f)
            {
                yield return null;
            }

            AsyncOperation loadMainOp = SceneManager.LoadSceneAsync(_currentStartingAreaScene, LoadSceneMode.Additive);
            loadMainOp.allowSceneActivation = false;

            while (loadMainOp.progress < 0.9f)
            {
                yield return null;
            }

            loadBlockOp.allowSceneActivation = true;
            loadMainOp.allowSceneActivation = true;

            while (!loadMainOp.isDone)
            {
                yield return null;
            }

            SceneManager.SetActiveScene(SceneManager.GetSceneByName(_currentEssentialScene));

            // Unload loading scene and switch to starting scene
            SceneManager.UnloadSceneAsync(_coreGameScenesReference.LoadingScreen);

            CurrentlyLoading = false;
            LoadProgress = 1f;
        }
#endif
    }
}