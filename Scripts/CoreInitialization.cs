// Copyright Isto Inc.

using Isto.Core.Achievements;
using Isto.Core.Configuration;
using Isto.Core.Localization;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.AddressableAssets.ResourceLocators;
using UnityEngine.ResourceManagement.AsyncOperations;
using Zenject;

#if PLATFORM_GAMECORE
using Isto.Core.UI;
#endif

namespace Isto.Core
{
    public class CoreInitialization : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] protected bool UseAddressables = true;

        // OTHER FIELDS

        protected VersionInfo VersionInfo;


        // INJECTION

        private IAchievements _achievements;
        private ILocalizationProvider _locProvider;

        [Inject]
        public void Inject(ILocalizationProvider locProvider, IAchievements achievements)
        {
            _locProvider = locProvider;
            _achievements = achievements;
        }


        // LIFECYCLE EVENTS

        // Early initialization of stuff that would be better outside of Zenject
        protected virtual void Awake()
        {
            VersionInfo = VersionInfo.LoadFromResources();
            Debug.Log(Application.productName + " v" + VersionInfo.ActiveVersion.FullVersionText);

#if PLATFORM_GAMECORE && !UNITY_EDITOR
            StartCoroutine(UnityEngine.GameCore.PlayerPrefs.InitializeAsync(() => { }));
#endif
            if (UseAddressables)
            {
                InitializeAddressables();
            }
        }

        protected virtual void Start()
        {
            // Getting and setting back the language makes sure we end up in something supported.
            // Make sure this happens before first localized content shows up in the game.
            // This is mostly for dev workflows across different builds.
            LanguageChanger.LanguageEnum currentLanguage = _locProvider.GetCurrentLanguage();
            _locProvider.SetLanguage(currentLanguage);

            _achievements.Initialize();
        }

        protected virtual void InitializePlayerPrefs()
        {
            Debug.Log("PlayerPrefs initialized.");
#if PLATFORM_GAMECORE
            // it seems Xbox doesn't remember the user's chosen settings from previous session so we will apply them at startup
            // TODO: this code is from Atrio. see what we actually require for GTW and for new settings menu contents
            /*if (PlayerPrefs.HasKey(UISettingsVideoSubState.REFRESH_RATE_PLAYER_PREFS_KEY))
            {
                RefreshRate savedRefreshRate = new RefreshRate
                {
                    numerator = (uint)PlayerPrefs.GetFloat(UISettingsVideoSubState.REFRESH_RATE_PLAYER_PREFS_KEY, defaultValue: 60f),
                    denominator = 1
                };
                int vsync = PlayerPrefs.GetInt(UISettingsVideoSubState.VSYNC_PLAYER_PREFS_KEY);
                Screen.SetResolution(Screen.width, Screen.height, Screen.fullScreenMode, savedRefreshRate);
                QualitySettings.vSyncCount = vsync;
            }*/
#endif
        }

        protected virtual void InitializeAddressables()
        {
            AsyncOperationHandle<IResourceLocator> handle = Addressables.InitializeAsync();

            handle.Completed += OnAddressablesInitialized;
        }


        // EVENTS

        private void OnAddressablesInitialized(AsyncOperationHandle<IResourceLocator> handle)
        {
            if (handle.Status == AsyncOperationStatus.Succeeded)
            {
                Debug.Log("Addressables initialized successfully!");
            }
            else
            {
                Debug.LogError("Failed to initialize Addressables!");
            }
            // TODO: Flag that loading can progress? But as long as there are no addressables in the title menu, we're probably OK.
        }
    }
}