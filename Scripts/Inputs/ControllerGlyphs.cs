// Copyright Isto Inc.
using Rewired;
using Rewired.Data.Mapping;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Inputs
{
#pragma warning disable 0649            // Hiding Unassigned warnings since ReWired expects certain properties but we're not currently using them

    /// <summary>
    /// Handles the setting and getting of button sprites for various controllers and keyboard
    /// </summary>
    public class ControllerGlyphs : MonoBehaviour
    {
        [SerializeField]
        private ControllerEntry[] controllers;

        [SerializeField]
        private Sprite defaultButtonGlyph;

        [SerializeField]
        private GlyphEntry[] keyboardMapping;

        [SerializeField]
        private GlyphEntry[] mouseMapping;

        public Sprite GetDefaultControllerGlyph()
        {
            return defaultButtonGlyph;
        }

        public Sprite GetGlyph(string action, Controls.InputDevice device)
        {
            InputAction inputAction = ReInput.mapping.GetAction(action.ToString()); // get the Action for the current string

            return GetGlyphForInputAction(inputAction, device);
        }

        public Sprite GetGlyph(int actionId, Controls.InputDevice device)
        {
            InputAction inputAction = ReInput.mapping.GetAction(actionId); // get the Action for the current string

            return GetGlyphForInputAction(inputAction, device);
        }

        private Sprite GetGlyphForInputAction(InputAction inputAction, Controls.InputDevice device)
        {
            if (inputAction != null)
            {
                // Get the Rewired Player
                Player p = ReInput.players.GetPlayer(0); // just use Player 0 in this example

                Controller activeController = GetActiveController(device, p);

                if (activeController == null || inputAction == null)
                    return null;

                switch (device)
                {
                    case Controls.InputDevice.Joystick:
                        return GetSpriteForController(activeController, inputAction.id, p);
                    case Controls.InputDevice.Keyboard:
                        Sprite keySprite = GetSpriteForController(activeController, inputAction.id, p);

                        // If no keyboard sprite found, try mouse
                        if (keySprite == null)
                            keySprite = GetSpriteForController(GetActiveController(Controls.InputDevice.Mouse, p), inputAction.id, p);

                        return keySprite;
                    case Controls.InputDevice.Mouse:
                        Sprite mouseSprite = GetSpriteForController(activeController, inputAction.id, p);

                        // If no mouse sprite found, try keyboard
                        if (mouseSprite == null)
                            mouseSprite = GetSpriteForController(GetActiveController(Controls.InputDevice.Keyboard, p), inputAction.id, p);

                        return mouseSprite;
                    default:
                        break;
                }
            }

            //Debug.LogFormat("Cannot find Glyph for action: {0} with input device: {1}", action, device.ToString());

            return null;
        }

        /// <summary>
        /// Attempts to find a sprite for the passed in controller and actionID.
        /// </summary>
        /// <param name="controller">Controller to check mapping for.</param>
        /// <param name="actionId">Rewired action ID.</param>
        /// <param name="p">ReWired Player</param>
        /// <returns></returns>
        private Sprite GetSpriteForController(Controller controller, int actionId, Player p)
        {
            // Find the first element mapped to this Action on this controller
            ActionElementMap aem = p.controllers.maps.GetFirstElementMapWithAction(controller, actionId, true);

            if (aem == null)
                aem = p.controllers.maps.GetFirstElementMapWithAction(controller, actionId, false);

            // If still no ActionElementMap found, return null as no controller mapping for this action
            if (aem == null)
                return null;

            switch (controller.type)
            {
                case ControllerType.Keyboard:
                    return GetKeyboardGlyph(aem.elementIdentifierId);
                case ControllerType.Mouse:
                    return GetMouseGlyph(aem.elementIdentifierId);
                case ControllerType.Joystick:
                    return GetJoystickGlyph((controller as Joystick).hardwareTypeGuid, aem.elementIdentifierId, aem.axisRange);
                case ControllerType.Custom:
                default:
                    Debug.LogError("Unknown Controller type used in ControllerGlyphs.");
                    break;
            }

            return null;
        }

        /// <summary>
        /// Gets the ReWired controller based on the Input Device passed in.
        /// </summary>
        /// <param name="device"></param>
        /// <returns></returns>
        private Controller GetActiveController(Controls.InputDevice device, Player p)
        {
            Controller activeController = null;

            if (p == null)
                return null;

            switch (device)
            {
                case Controls.InputDevice.Joystick:
                    activeController = p.controllers.GetController(ControllerType.Joystick, 0);
                    break;
                case Controls.InputDevice.Keyboard:
                    activeController = p.controllers.GetController(ControllerType.Keyboard, 0);
                    break;
                case Controls.InputDevice.Mouse:
                    activeController = p.controllers.GetController(ControllerType.Mouse, 0);
                    break;
                default:
                    break;
            }

            // If controller still null, just use the keyboard
            if (activeController == null)
            {
                activeController = p.controllers.Keyboard;
            }

            return activeController;
        }

        public Sprite GetJoystickGlyph(System.Guid joystickGuid, int elementIdentifierId, AxisRange axisRange)
        {
            if (controllers == null)
                return null;

            // Try to find the glyph

            // Alternate Joysticks is there to allow a list of "specific" controller to have the same glyphs.
            // This is because right now we basically just have specific glyphs if you're on a sony controller or xbox controller.
            // In reality even those controllers groups have some slight differences that we might or might need to handle.
            // If we do take the time to handle those differences at some point, we can remove this code and the alternate list.
            ControllerEntry compatible = controllers.Where(x => x.alternateJoysticks.Where(y => y.Guid == joystickGuid).FirstOrDefault() != null).FirstOrDefault();
            if (compatible == null)
            {
                compatible = controllers[0];
            }
            if (compatible != null)
            {
                return compatible.GetGlyph(elementIdentifierId, axisRange);
            }

            // Original logic that considers only one controller per glyph set
            for (int i = 0; i < controllers.Length; i++)
            {
                if (controllers[i] == null)
                    continue;
                if (controllers[i].joystick == null)
                    continue; // no joystick assigned
                if (controllers[i].joystick.Guid != joystickGuid)
                    continue; // guid does not match
                return controllers[i].GetGlyph(elementIdentifierId, axisRange);
            }

            return null;
        }

        public Sprite GetMouseGlyph(int elementIdentifierId)
        {
            if (mouseMapping == null)
                return null;

            for (int i = 0; i < mouseMapping.Length; i++)
            {
                if (mouseMapping[i] == null)
                    continue;
                if (mouseMapping[i].elementIdentifierId == elementIdentifierId)
                    return mouseMapping[i].glyph;
            }

            return null;
        }

        public Sprite GetKeyboardGlyph(int elementIdentifierId)
        {
            if (keyboardMapping == null)
                return null;

            for (int i = 0; i < keyboardMapping.Length; i++)
            {
                if (keyboardMapping[i] == null)
                    continue;
                if (keyboardMapping[i].elementIdentifierId == elementIdentifierId)
                    return keyboardMapping[i].glyph;
            }

            return null;
        }

        [System.Serializable]
        private class ControllerEntry
        {
            public string name;
            // This must be linked to the HardwareJoystickMap located in Rewired/Internal/Data/Controllers/HardwareMaps/Joysticks
            public HardwareJoystickMap joystick;
            public List<HardwareJoystickMap> alternateJoysticks;

            public GlyphEntry[] glyphs;

            public Sprite GetGlyph(int elementIdentifierId, AxisRange axisRange)
            {
                if (glyphs == null)
                    return null;
                for (int i = 0; i < glyphs.Length; i++)
                {
                    if (glyphs[i] == null)
                        continue;
                    if (glyphs[i].elementIdentifierId != elementIdentifierId)
                        continue;
                    return glyphs[i].GetGlyph(axisRange);
                }
                return null;
            }
        }

        [System.Serializable]
        private class GlyphEntry
        {
            public int elementIdentifierId;
            public Sprite glyph;
            public Sprite glyphPos;
            public Sprite glyphNeg;

            public Sprite GetGlyph(AxisRange axisRange)
            {
                switch (axisRange)
                {
                    case AxisRange.Full:
                        return glyph;
                    case AxisRange.Positive:
                        return glyphPos != null ? glyphPos : glyph;
                    case AxisRange.Negative:
                        return glyphNeg != null ? glyphNeg : glyph;
                }
                return null;
            }
        }
    }
}