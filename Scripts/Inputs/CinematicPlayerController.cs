// Copyright Isto Inc.
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Inputs
{
    public class CinematicPlayerController : MonoBehaviour
    {
        public List<Transform> wayPoints;

        private ScriptedControls _controls;

        private void Awake()
        {
            _controls = FindObjectOfType<ScriptedControls>();

            if (_controls == null)
                Debug.LogError("No Scripted Controls found in scene by " + gameObject.name);
        }

        // Methods to be called from Animation/Timeline

        public void MoveToWayPoint(int index)
        {
            if (index >= wayPoints.Count)
            {
                Debug.LogError($"Not enough waypoints in list on {gameObject.name}for call to MoveToWayPoint. Index:{index}");
                return;
            }

            StartCoroutine(MoveCoroutine(wayPoints[index].position));
        }

        // Internal methods to perform actual actions

        private IEnumerator MoveCoroutine(Vector3 targetGroundPosition)
        {
            _controls.ClearAction();

            _controls.mouseGroundPosition = targetGroundPosition;
            _controls.SetNextAction(UserActions.INTERACT);

            yield return null;

            _controls.ClearAction();
        }

        /// <summary>
        /// Sets the controls to have this action happening for one frame
        /// </summary>
        /// <param name="action"></param>
        /// <returns></returns>
        private IEnumerator TriggerActionCortoutine(UserActions action)
        {
            _controls.ClearAction();

            _controls.SetNextAction(action);

            yield return null;

            _controls.ClearAction();
        }

        // Testing methods

        [ContextMenu("Move to waypoint 0")]
        public void MoveToWaypointZero()
        {
            StartCoroutine(MoveCoroutine(wayPoints[0].position));
        }
    }
}