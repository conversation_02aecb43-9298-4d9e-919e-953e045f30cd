// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    // It would be great if we could generalize this concept and have it be in core to build upon in our projects.
    // However as it is, this has no business in core. It's been copied in core to allow BuildingFirstConnectAction to reference it.
    public class UIAccessManager 
    {
        // Definitions

        private static readonly int CANVASGROUP_FLAG_BITCOUNT = 32;

        // TODO: this should be refactored into a ExpandableFlags as part of Core refactoring for a future task that
        // <PERSON> is supposed to create/have created
        [Flags]
        public enum CanvasGroupId : int // has to be an int or we lose the flags dropdown style property drawer
        {
            None                            = 0,
            SceneBlackOut                   = 1 << 0,

            UIGameplay                      = 1 << 5,
            UIGameplayHideInDark            = 1 << 6, //This is a subcanvasgroup of UIGameplay

            UIGameplayIndependent           = 1 << 10,
            UIGameplayIndependentHideInDark = 1 << 11, //This is a subcanvasgroup of UIGameplayIndependent

            UIEquipMenu                     = 1 << 15,
            UIEquipMenuItems                = 1 << 16, //This is a subcanvasgroup of UIEquipMenu

            //This subset of groups was taken from existing code that manipulated these CanvasGroups together
            UIInitial                       = UIGameplay | UIGameplayIndependent | UIEquipMenu,

            TutorialActionText              = 1 << 30, //sub group of Tutorial
            Tutorial                        = 1 << 31, //largest allowed flag

            HUDGroups                       = UIGameplay | UIGameplayIndependent | UIEquipMenu | Tutorial,

            All                             = ~0
        }

        // Private Variables

        private List<CanvasGroupId> _allIndividualFlags = Enum.GetValues(typeof(CanvasGroupId)).Cast<CanvasGroupId>().ToList();
        private Dictionary<CanvasGroupId, CanvasGroup> _canvasGroups;

        // Methods

        [Inject]
        public void Inject([Inject(Id = "SceneBlackOut")] CanvasGroup sceneBlackOut,
                           //[Inject(Id = "UIEquipMenu")] CanvasGroup equipUI,
                           [Inject(Id = "UIGameplay")] CanvasGroup gameplayUI
                           //[Inject(Id = "UIGameplayHideInDark")] CanvasGroup gameplayUIHiddenWhenPlayerInDark,
                           //[Inject(Id = "UIEquippedItems")] CanvasGroup equippedItemSlots,
                           //[Inject(Id = "TutorialContainer")] CanvasGroup tutorialCanvas,
                           //[Inject(Id = "UITutorialActionText")] CanvasGroup tutorialActionText,
                           //[Inject(Id = "UIGameplayIndependent")] CanvasGroup gameplayIndependentUI,
                           //[Inject(Id = "UIGameplayIndependentHideInDark")] CanvasGroup gameplayIndependentUIHiddenWhenPlayerInDark
                          )
        {
            // Note: this is the list of canvas groups that was in atrio, ported over here hastily.
            //TODO: these groups need to be optional in core logic, or there should be some version of them always
            //present, or we simply make sure those that have actualy business in core are introduced but the rest is
            //cleaned up from core and you add them in your project if needed.
            // Consider: several of the systems refered in the CanvasGroupId list might exist in Core but might not
            // always be included in your scene setup depending on the project. So optional might make sense as a rule.
            _canvasGroups = new Dictionary<CanvasGroupId, CanvasGroup>();
            _canvasGroups.Add(CanvasGroupId.SceneBlackOut, sceneBlackOut);
            //_canvasGroups.Add(CanvasGroupId.UIEquipMenu, equipUI);
            _canvasGroups.Add(CanvasGroupId.UIGameplay, gameplayUI);
            //_canvasGroups.Add(CanvasGroupId.UIGameplayHideInDark, gameplayUIHiddenWhenPlayerInDark);
            //_canvasGroups.Add(CanvasGroupId.UIEquipMenuItems, equippedItemSlots);
            //_canvasGroups.Add(CanvasGroupId.Tutorial, tutorialCanvas);
            //_canvasGroups.Add(CanvasGroupId.TutorialActionText, tutorialActionText);
            //_canvasGroups.Add(CanvasGroupId.UIGameplayIndependent, gameplayIndependentUI);
            //_canvasGroups.Add(CanvasGroupId.UIGameplayIndependentHideInDark, gameplayIndependentUIHiddenWhenPlayerInDark);
        }
        
        public CanvasGroup GetSingleCanvasGroup(CanvasGroupId target)
        {
            return _canvasGroups[target];
        }

        public List<CanvasGroup> GetCanvasGroups(CanvasGroupId targetCanvasFlags)
        {
            List<CanvasGroup> canvasGroups = new List<CanvasGroup>();
            for (int i = 0; i < CANVASGROUP_FLAG_BITCOUNT; i++)
            {
                UInt64 flagValue = 1ul << i;
                CanvasGroupId canvasFlag = (CanvasGroupId)flagValue;

                if (_allIndividualFlags.Contains(canvasFlag)
                    && targetCanvasFlags.HasFlag(canvasFlag)
                    && _canvasGroups.ContainsKey(canvasFlag))
                {
                    canvasGroups.Add(_canvasGroups[canvasFlag]);
                }
            }
            return canvasGroups;

            //Alternate algorithm:
            /*var value = Items.Foo | Items.Bar;
            var values = value.ToString()
                         .Split(new[] { ", " }, StringSplitOptions.None)
                         .Select(v => (Items)Enum.Parse(typeof(Items), v));*/
        }
    }
}
