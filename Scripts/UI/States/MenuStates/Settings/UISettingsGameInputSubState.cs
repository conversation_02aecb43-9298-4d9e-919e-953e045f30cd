// Copyright Isto Inc.

using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Rewired;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Sub-state class for managing the games input sub categories. This generic class is used to handle Keyboard and Mouse
    /// as well as the controller. It is pushed onto the UISettingsMenu stack via UISettingsControlsSubState.
    /// </summary
    public class UISettingsGameInputSubState : UISettingsSubCategorySubState
    {
        // UNITY HOOKUP

        [Header("First Selectable Setup")]
        [SerializeField] private UISettingsSelectable _firstInputSelectable;

        [Header("Controller Maps")]
        [SerializeField] private List<ControllerType> _controllerTypes;


        // OTHER FIELDS

        protected UIControlRebindingBase[] _keyRebindings;

        private bool _hasChanges;

        // PROPERTIES

        public UIControlRebindingBase[] KeyRebindings => _keyRebindings;
        public override bool HasChanges => _hasChanges;
        public override Selectable DefaultSelectable => _firstInputSelectable;

        public bool KeepNavInSubTabButtonsBar { get; set; }

        // INJECTION

        private UserKeybindingDataStore _dataStore;

        [Inject]
        public virtual void Inject(UserKeybindingDataStore dataStore)
        {
            _dataStore = dataStore;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _keyRebindings = GetComponentsInChildren<UIControlRebindingBase>();
        }

        private void Start()
        {
            SetupSelectableNavigation(transform);
        }

        public override void Enter(MonoStateMachine controller)
        {
            // Not sure why, my selection is not painting correctly any time I tab into the page except the first.
            // I'm assuming it's related to being already selected.
            // This hack removing the selection seems to force it to refresh and always be selected yellow as expected.
            // Will need to look into this later when we have more time.
            EventSystem.current.SetSelectedGameObject(null);

            base.Enter(controller);
            RefreshControlsDisplay();
        }


        // ACCESSORS

        private void SetHasChanges(bool hasChanges)
        {
            if (_hasChanges == hasChanges)
                return;

            SetButtonEnabled(_applySettingsButton, hasChanges);
            SetButtonEnabled(_discardSettingsButton, hasChanges);
            _hasChanges = hasChanges;
        }

        private void SetButtonEnabled(CoreButton button, bool state)
        {
            if (button.enabled != state)
            {
                button.enabled = state;
            }
        }


        // OTHER METHODS

        protected override void SetSelectionToDefaultSelectable()
        {
            if (!Controls.UsingController)
                return;

            if (KeepNavInSubTabButtonsBar && SubTabButton != null)
            {
                SubTabButton.Select();
            }
            else
            {
                base.SetSelectionToDefaultSelectable();
            }
        }

        public override void ApplySettingsChanges()
        {
            base.ApplySettingsChanges();

            _dataStore.Save();
            UpdateControlKeys();
            SetHasChanges(false);
            Events.RaiseEvent(Events.CONTROLS_CHANGED);
        }

        public override void DiscardSettingsChanges()
        {
            base.DiscardSettingsChanges();

            _dataStore.Load();
            RefreshControlsDisplay();
            SetHasChanges(false);
            Events.RaiseEvent(Events.CONTROLS_CHANGED);
        }

        public override void ResetToDefaults()
        {
            Player player = ReInput.players.GetPlayer(Controls.DEFAULT_REWIRED_PLAYER_ID);

            foreach (ControllerType currentControllerType in _controllerTypes)
            {
                player.controllers.maps.LoadDefaultMaps(currentControllerType);
            }

            RefreshControlsDisplay();
            ApplySettingsChanges();
        }

        public void RefreshControlsDisplay()
        {
            if(_keyRebindings == null)
                return;

            foreach (UIControlRebindingBase currentRebinding in _keyRebindings)
            {
                currentRebinding.RefreshDisplay();
            }
        }

        public void VerifyRemapDirtyFlags()
        {
            bool hasChanges = false;

            foreach (UISettingsSelectable currentSelectable in _selectableSettings)
            {
                if (currentSelectable.IsDirty)
                {
                    hasChanges = true;
                    break;
                }
            }

            SetHasChanges(hasChanges);
        }

        private void UpdateControlKeys()
        {
            if(_keyRebindings == null)
                return;

            foreach (UIControlRebindingBase currentRebinding in _keyRebindings)
            {
                currentRebinding.ClearRebindingCache();
            }
        }
    }
}