// Copyright Isto Inc.

using Isto.Core.Inputs;
using Rewired;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Displays a button that maps to one of the two poles of an axis.
    /// Meant to be used for the keyboard to handle axis inputs (like a and d for horizontal movement)
    /// </summary>
    public class UIAxisRebinding : UIControlRebindingBase
    {
        public override string InputActionName => _axis.ToString();
        public override bool IsAxis => true;
        public override AxisRange ActionAxisRange
        {
            get
            {
                if (_aem != null)
                    return _aem.axisContribution == Pole.Positive ? AxisRange.Positive : AxisRange.Negative;
                else
                    return _axisDirection == Pole.Negative ? AxisRange.Negative : AxisRange.Positive;
            }
        }

        public override Pole AxisDirection => _axisDirection;

        [Header("Axis To Rebind")]
        [SerializeField] private Controls.MovementAxis _axis;
        [SerializeField] private Pole _axisDirection;

        public override void RefreshDisplay()
        {
            _actionId = ReInput.mapping.GetActionId(_axis.ToString());
            _map = GetControllerMapForCategory(_category);
            _aem = GetFirstElementMapFromControllerMap(_map, _axis.ToString(), _axisDirection);

            if (_aem != null)
            {
                Sprite actionSprite = GetGlyphForAction(_aem);

                if (actionSprite != null)
                {
                    _keyImage.sprite = actionSprite;
                    _keyImage.enabled = true;

                    _controlKeyTextbox.enabled = false;
                }
                else
                {
                    _keyImage.enabled = false;
                    _controlKeyTextbox.text = SplitString(_aem.keyCode.ToString());
                    _controlKeyTextbox.enabled = true;
                }
            }
            // If null no mapping currently exists
            else
            {
                _keyImage.sprite = null;
                _controlKeyTextbox.text = "";
                _controlKeyTextbox.enabled = true;
            }

            if (_glyphOverride != null)
            {
                _keyImage.sprite = _glyphOverride;
            }

            if (!_isInitialized)
            {
                _cachedSprite = _keyImage.sprite;
                _isInitialized = true;
            }
            else
            {
                Button.SetDirtyFlag(_cachedSprite != _keyImage.sprite);
            }
        }

        public string SplitString(string stringToChange)
        {
            if (string.IsNullOrEmpty(stringToChange))
                return "";

            var r = new Regex(@"
                (?<=[A-Z])(?=[A-Z][a-z]) |
                 (?<=[^A-Z])(?=[A-Z]) |
                 (?<=[A-Za-z])(?=[^A-Za-z])", RegexOptions.IgnorePatternWhitespace);

            return r.Replace(stringToChange, " ");
        }
    }
}