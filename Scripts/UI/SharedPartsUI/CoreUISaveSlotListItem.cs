// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Localization;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class CoreUISaveSlotListItem : MonoBehaviour
    {
        // UNITY HOOKUP
        
        [SerializeField] private TextMeshProUGUI slotNameLabel = null;
        [SerializeField] private TextMeshProUGUI gameModeLabel = null;

        // OTHER FIELDS
        
        private CoreUISaveSlotList _parentMenu = null;
        private int _slotNumber;
        private GameStateSaveData _metaData;
        
        // PROPERTIES
        
        public int SlotNumber { get { return _slotNumber; } }
        public GameStateSaveData MetaData { get { return _metaData; } }
        public RectTransform RectTransform => this.transform as RectTransform;
        

        // INJECTION
        
        private GameState _gameState;

        [Inject]
        public void Inject(GameState gameState)
        {
            _gameState = gameState;
        }

        [ContextMenu("Select this")]
        public void Select()
        {
            Button button = this.GetComponent<Button>();
            if (button != null)
                button.Select();
        }

        public void SetupSaveSlot(int slotnumber, GameStateSaveData metaData)
        {
            _slotNumber = slotnumber;
            _metaData = metaData;

            _parentMenu = transform.parent.GetComponent<CoreUISaveSlotList>();
            Debug.Assert(_parentMenu != null, "UISaveSlotListItem is meant to exist as a child of a UISaveSlotList. Cannot find parent UISaveSlotList.");

            Loc.SetTMProLocalized(slotNameLabel, metaData.saveSlotName);

            var query = _gameState.settingsMapping.GetGameModes().Where(x => x.InternalName == metaData.gameModeName);

            if (query.Count() < 1)
            {
                Debug.LogWarning($"Trying to display a save slot that is registered to unknown game mode: {metaData.gameModeName}");
                Loc.SetTMPro(gameModeLabel, Constants.GAMEMODE_ERROR);
                //We're allowing the loading of bad files in case someone is internally trying to make something weird work
                //_slotButton.interactable = false;
            }
            else
            {
                bool saveOutOfDate = IsSaveOutOfDate(metaData, out _);

                if (saveOutOfDate)
                    Loc.SetTMProLocalized(gameModeLabel, Loc.Get(query.First().NameLoc) + Loc.Get(Constants.SAVE_OUT_OF_DATE_SUFFIX));
                else
                    Loc.SetTMPro(gameModeLabel, query.First().NameLoc);
            }
        }

        protected virtual bool IsSaveOutOfDate(GameStateSaveData gameStateData, out string versionToUpgradeTo)
        {
            versionToUpgradeTo = "";
            return false;
        }

        // Hooked up to OnClick event from Button component on our gameobject
        public void SlotButtonClicked()
        {
            _parentMenu.SlotButtonClicked(this);
        }
    }
}