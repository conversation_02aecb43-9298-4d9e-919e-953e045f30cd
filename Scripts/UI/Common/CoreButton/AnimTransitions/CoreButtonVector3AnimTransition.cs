using Isto.Core.UI.ButtonStyles;
using UnityEngine;

namespace Isto.Core.UI
{
    public abstract class CoreButtonVector3AnimTransition : CoreButtonAnimTransitionBase<Vector3Style, Vector3StateData, Vector3StateDataValue>
    {
        protected override bool Evaluate(CoreButton.CoreButtonSelectionState state, float time)
        {
            IStateData stateData = GetStateData();
            if (stateData != null)
            {
                Vector3Data animCurve = stateData.GetState(state) as Vector3Data;
                if (animCurve != null)
                {
                    SetObjectProperties(animCurve.Evaluate(time));
                    return time >= animCurve.AnimationTimeLength;
                }
            }

            return true;
        }
        
        protected abstract void SetObjectProperties(Vector3 value);
    }
}