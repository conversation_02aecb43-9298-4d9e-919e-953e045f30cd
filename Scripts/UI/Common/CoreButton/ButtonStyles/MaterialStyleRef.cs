// unset
using UnityEngine;

namespace Isto.Core.UI.ButtonStyles
{
    [CreateAssetMenu(fileName = "MaterialStyleRef", menuName = "Scriptables/Core Button/Material Style Ref")]
    [System.Serializable]
    public class MaterialStyleRef : MaterialStyle
    {
        public MaterialStateDataRef stateData = new MaterialStateDataRef();
        
        public override IStateData GetStateData()
        {
            return stateData;
        }
    }
}