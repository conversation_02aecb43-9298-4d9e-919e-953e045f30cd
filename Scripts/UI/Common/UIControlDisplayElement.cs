// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Enums;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class UIControlDisplayElement : MonoBehaviour
    {
        // Public Variables

        public bool hideForMouseInput;
        public bool hideForControllerInput;

        [EnumDropdown(typeof(UserActions))]
        [SerializeField]
        private string _userAction;

        [Space(20)]
        public bool useModifier;

        public Image actionImage;
        public Image modifierImage;

        [Tooltip("This is optional. Do not assign it and the layout will be preserved when the element is hidden")]
        public LayoutElement layoutElement;

        [SerializeField] private LocalizedString _actionDescription;
        [EnumDropdown(typeof(UserActions))]
        [SerializeField]
        private string _modifierAction;

        // Private Variables

        private IControls _controls;
        private TextMeshProUGUI _descriptionText;

        // Lifecycle Events

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }

        private void Awake()
        {
            _descriptionText = GetComponentInChildren<TextMeshProUGUI>();
        }

        private void Start()
        {
            UpdateControlDisplay();
        }

        private void OnEnable()
        {
            UpdateControlDisplay();
            RegisterEvents();
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        // events

        private void RegisterEvents()
        {
            Events.Subscribe(Events.SETTINGS_CHANGED, Events_OnSettingsChanged);
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnSettingsChanged);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.SETTINGS_CHANGED, Events_OnSettingsChanged);
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnSettingsChanged);
        }

        private void Events_OnSettingsChanged()
        {
            UpdateControlDisplay();
        }

        // accessors

        public void SetAction(UserActions newAction)
        {
            if(newAction == null)
            {
                newAction = UserActions.NONE;
            }    
            _userAction = newAction.Name;
            UpdateControlDisplay();
        }

        public void SetActionDescription(LocalizedString description)
        {
            _actionDescription = description;
            UpdateControlDisplay();
        }

        private bool IsUserActionInvalid()
        {
            if (_userAction == null)
            {
                Debug.LogError($"Action needs to be added to the Display Element for the GameObject {gameObject.name}");
                return true;
            }
            return false;
        }

        private void UpdateControlDisplay()
        {
            if (ShouldHideForInput() || IsUserActionInvalid())
            {
                DisableDisplay();
                return;
            }

            UserActions action = UserActions.GetByValue(_userAction);
            Sprite actionSprite = null;
            if(action != null)
            {
                actionSprite = _controls.GetIconForAction(action);
            }
            if (actionSprite == null)
            {
                DisableDisplay();
                return;
            }

            if (actionImage != null && actionSprite != null)
            {
                if (layoutElement)
                    layoutElement.enabled = true;

                actionImage.sprite = actionSprite;
                actionImage.enabled = true;

                if (modifierImage != null)
                {
                    UserActions modifier = UserActions.GetByValue(_modifierAction);
                    modifierImage.sprite = _controls.GetIconForAction(modifier);

                    if (useModifier)
                        modifierImage.enabled = true;
                    else
                        modifierImage.enabled = false;
                }
            }
            else
            {
                DisableDisplay();
            }

            if (_descriptionText != null)
            {
                _descriptionText.enabled = true;
                if (_descriptionText != null && _actionDescription != null)
                    Loc.SetTMPro(_descriptionText, _actionDescription);
            }
        }

        private bool ShouldHideForInput()
        {
            return (Controls.UsingController && hideForControllerInput) || (!Controls.UsingController && hideForMouseInput);
        }

        private void DisableDisplay()
        {
            if (actionImage)
                actionImage.enabled = false;

            if (modifierImage)
                modifierImage.enabled = false;

            if (_descriptionText)
                _descriptionText.enabled = false;

            if (layoutElement)
                layoutElement.enabled = false;
        }
    }
}