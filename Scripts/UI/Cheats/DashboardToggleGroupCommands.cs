// Copyright Isto Inc.

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    public class DashboardToggleGroupCommands : MonoBehaviour
    {
        // Definitions
        [Serializable]
        private class ToggleData
        {
            public string Name;
            public Toggle Toggle;
            public List<UIConsole.DeveloperConsoleCommandEntry> ActivationCommands;
            public List<UIConsole.DeveloperConsoleCommandEntry> DeactivationCommands;
        }

        // Public Variables


        // Assign in inspector
        [SerializeField] private ToggleGroup _group = default;
        [SerializeField] private ToggleData[] _toggles = default;

        // Private Variables
        private CheatMenuState _console;
        private ColorBlock _normalToggleColors;
        private ColorBlock _engagedToggleColors;
        private ToggleData _currentEngagedToggle;

        // Lifecycle Events

        private void Start()
        {
            _console = this.gameObject.GetComponentInParent<CheatMenuState>();

            if (_group == null || _toggles == null || _toggles.Length == 0 || _console == null)
                Debug.LogError("DashboardToggleGroupCommands not setup properly in the DeveloperConsole prefab", this);
        }

        private void OnEnable()
        {
            _normalToggleColors = _toggles[0].Toggle.colors;
            _engagedToggleColors = _normalToggleColors;
            _engagedToggleColors.normalColor = _normalToggleColors.pressedColor;
            _engagedToggleColors.pressedColor = _normalToggleColors.normalColor;

            //register events
            foreach (ToggleData data in _toggles)
            {
                if (data.Toggle.isOn)
                    _currentEngagedToggle = data;
                UpdateDashboard(data.Toggle, data.Toggle.isOn);
                data.Toggle.onValueChanged.AddListener(value => OnTogglePressed(data, value));
            }
        }

        private void OnDisable()
        {
            //unregister events
            foreach (ToggleData toggleData in _toggles)
            {
                toggleData.Toggle.onValueChanged.RemoveAllListeners();
            }
        }

        private void OnDestroy()
        {

        }


        // Methods		

        private void OnTogglePressed(ToggleData data, bool value)
        {
            if (value)
            {
                if (_currentEngagedToggle != data)
                {
                    _console.Run(data.ActivationCommands);
                    _currentEngagedToggle = data;
                }
            }
            else
            {
                _console.Run(data.DeactivationCommands);
            }

            UpdateDashboard(data.Toggle, value);
        }

        private void UpdateDashboard(Toggle toggle, bool engaged)
        {
            if (engaged)
            {
                toggle.colors = _engagedToggleColors;
            }
            else
            {
                toggle.colors = _normalToggleColors;
            }
        }
    }
}