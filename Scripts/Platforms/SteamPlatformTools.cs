// Copyright Isto Inc.
#if PLATFORM_STEAM
using Steamworks;
using System;
using UnityEngine;

namespace Isto.Core.Platforms
{
    public class SteamPlatformTools : IPlatformTools
    {
        public uint storeAppID;

        public SteamPlatformTools(uint storeAppID)
        {
            this.storeAppID = storeAppID;
        }

        public string GetPlayerName()
        {
            if(SteamManager.Initialized)
            {
                string name = SteamFriends.GetPersonaName();
                return name;
            }
            else
            {
                Debug.LogError("Steam not initialized");
                return "";
            }
        }

        public bool TryGetPurchaseDate(out DateTime purchaseDate)
        {
            if (SteamManager.Initialized)
            {
                uint unixTime = SteamApps.GetEarliestPurchaseUnixTime(new AppId_t(storeAppID));

                DateTimeOffset offsetTime = DateTimeOffset.FromUnixTimeSeconds(unixTime);

                purchaseDate = offsetTime.LocalDateTime;
                return true;
            }
            else
            {
                Debug.LogError("Steam not initialized");
                purchaseDate = DateTime.Now;
                return false;
            }
        }
    }
}

#endif // PLATFORM_STEAM