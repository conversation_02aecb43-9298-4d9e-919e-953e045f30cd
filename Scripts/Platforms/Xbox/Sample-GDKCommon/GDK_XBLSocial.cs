
#if PLATFORM_GAMECORE
using System;
using System.Collections;
using System.Collections.Generic;

using Unity.GameCore;

using UnityEngine;
#endif

namespace Isto.Core.Platforms.Xbox
{

	public class GDK_XBLSocial
	{
		public enum State
		{
			NoCurrentUser,
			ProcessingAddUser,
			Available,
			FetchingFriends,
		}

#if PLATFORM_GAMECORE
		//----------------------------------------------------------------------------
		public State CurrentState
		{
			get { return m_currentState; }
			private set
			{
				if (value != m_currentState)
					Debug.Log($"Changing State from: {m_currentState} to {value}");
				m_currentState = value;
			}
		}

		//----------------------------------------------------------------------------
		public bool CurrentUserIsValid => (CurrentUser?.IsValid() ?? false);

		//----------------------------------------------------------------------------
		public GDK_XUserManager.XUserData CurrentUser => m_currentXUser;

		//----------------------------------------------------------------------------
		public void RequestSetCurrentUser(GDK_XUserManager.XUserData xUserData, System.Action<Int32> cbOnOperationCompleted)
		{
			Debug.Assert((null == m_currentXUser), $"CurrentUser must be null! is currently {(m_currentXUser?.m_userGamertag ?? "null")}");
			Debug.Assert((State.NoCurrentUser == CurrentState), $"CurrentState is unexpected at this time {CurrentState}");

			m_parentMonobehaviour.StartCoroutine(CO_HandleAddUserToSocialManager(xUserData, cbOnOperationCompleted));
		}

		//----------------------------------------------------------------------------
		public Int32 RemoveCurrentUser()
		{
			Debug.Assert((null != m_currentXUser), "CurrentUser must not be null!");
			Debug.Assert((State.NoCurrentUser != CurrentState), $"CurrentState is unexpected at this time {CurrentState}");

			return InternalRemoveCurrentUser(InternalRemoveCurrentUserParam.normalOperation);
		}

		//----------------------------------------------------------------------------
		public Int32 RemoveUserOnSignOut()
		{
			Debug.Assert((null != m_currentXUser), "CurrentUser must not be null!");
			Debug.Assert((State.NoCurrentUser != CurrentState), $"CurrentState is unexpected at this time {CurrentState}");

			return InternalRemoveCurrentUser(InternalRemoveCurrentUserParam.userHasSignedOut);
		}

		//----------------------------------------------------------------------------
		public void Update()
		{
			try
			{
				SDK.XBL.XblSocialManagerDoWork(out var xblSocialManagerEvents);

				if (null != xblSocialManagerEvents)
				{
					foreach (var xblEvent in xblSocialManagerEvents)
					{
						Debug.Log($"XblSocialManagerDoWork event: {xblEvent.EventType} HR: {PLHR.GetName(xblEvent.Hr)}");
						m_onSocialManagerEvent?.Invoke(xblEvent);
					}
				}
			}
			catch (Exception Ex)
			{
				Debug.Log($"XblSocialManagerDoWork exception:{PLHR.GetName(Ex.HResult)} - {Ex.Message}");
			}
		}

		//----------------------------------------------------------------------------
		public GDK_XBLSocial(MonoBehaviour parentMonobehaviour)
		{
			m_parentMonobehaviour = parentMonobehaviour;
		}

		//----------------------------------------------------------------------------
		public void RequestFriendListForCurrentUser(GDK_XUserManager.XUserData xUserData, List<ulong> outListOfFriendXUIDs, Action<Int32> onFriendListOperationFinished)
		{
			m_parentMonobehaviour.StartCoroutine(CO_HandleFetchFriendListForUser(xUserData, outListOfFriendXUIDs, onFriendListOperationFinished));
		}


		//----------------------------------------------------------------------------
		private readonly MonoBehaviour m_parentMonobehaviour = null;
		private State m_currentState = State.NoCurrentUser;
		private GDK_XUserManager.XUserData m_currentXUser = null;
		private XblSocialManagerUserGroupHandle m_owningUserGroupHandleFriends = null;
		private event Action<XblSocialManagerEvent> m_onSocialManagerEvent;

		//----------------------------------------------------------------------------
		private IEnumerator CO_HandleAddUserToSocialManager(GDK_XUserManager.XUserData xUserData, Action<Int32> onAddUserOperationFinished)
		{
			CurrentState = State.ProcessingAddUser;

			Int32 gdkHResultOnCompletion = PLHR.Invalid;

			// call add user - can fail on call as well as during processing
			{
				var gdkHResult = SDK.XBL.XblSocialManagerAddLocalUser(xUserData.m_userHandle, XblSocialManagerExtraDetailLevel.NoExtraDetail);
				gdkHResultOnCompletion = gdkHResult;
			}

			Debug.Log($"SDK.XBL.XblSocialManagerAddLocalUser returned on calling: {PLHR.GetName(gdkHResultOnCompletion)}");
			if (!HR.SUCCEEDED(gdkHResultOnCompletion))
			{
				goto EXIT_COROUTINE;
			}

			// wait for SocialManager to send the add user event 
			{
				var gdkHResult = PLHR.Invalid;

				Action<XblSocialManagerEvent> onEventAction =
				(xblEvent) =>
				{
					switch (xblEvent.EventType)
					{
						case XblSocialManagerEventType.LocalUserAdded:
							gdkHResult = xblEvent.Hr;
							break;
					}
				};

				// m_onSocialManagerEvent is sent by this.Update 
				m_onSocialManagerEvent += onEventAction;

				while (PLHR.Invalid == gdkHResult)
				{
					yield return null;
				}

				m_onSocialManagerEvent -= onEventAction;

				gdkHResultOnCompletion = gdkHResult;
			}

			Debug.Log($"SDK.XBL.XblSocialManagerAddLocalUser completed with: {PLHR.GetName(gdkHResultOnCompletion)}");
			if (!HR.SUCCEEDED(gdkHResultOnCompletion))
			{
				goto EXIT_COROUTINE;
			}

			// create a social group for the user
			{
				var gdkHResult = SDK.XBL.XblSocialManagerCreateSocialUserGroupFromFilters(xUserData.m_userHandle, XblPresenceFilter.All, XblRelationshipFilter.Friends, out m_owningUserGroupHandleFriends);
				gdkHResultOnCompletion = gdkHResult;
			}

			Debug.Log($"SDK.XBL.XblSocialManagerAddLocalUser returned on calling: {PLHR.GetName(gdkHResultOnCompletion)}");
			if (!HR.SUCCEEDED(gdkHResultOnCompletion))
			{
				goto EXIT_COROUTINE;
			}

			// wait for the social group to load
			{
				var gdkHResult = PLHR.Invalid;

				Action<XblSocialManagerEvent> onEventAction =
				(xblEvent) =>
				{
					switch (xblEvent.EventType)
					{
						case XblSocialManagerEventType.SocialUserGroupLoaded:
							gdkHResult = xblEvent.Hr;
							break;
					}
				};

				// m_onSocialManagerEvent is sent by this.Update 
				m_onSocialManagerEvent += onEventAction;

				while (PLHR.Invalid == gdkHResult)
				{
					yield return null;
				}

				m_onSocialManagerEvent -= onEventAction;

				gdkHResultOnCompletion = gdkHResult;
			}

			// success / failure - only become available on success
			Debug.Log($"SDK.XBL.XblSocialManagerCreateSocialUserGroupFromFilters completed with: {PLHR.GetName(gdkHResultOnCompletion)}");
			if (HR.SUCCEEDED(gdkHResultOnCompletion))
			{
				m_currentXUser = xUserData;
				CurrentState = State.Available;
			}
			else
			{
				CurrentState = State.NoCurrentUser;
			}

			EXIT_COROUTINE:
			if ((!HR.SUCCEEDED(gdkHResultOnCompletion))
				&& (null != m_owningUserGroupHandleFriends))
			{
				// these need to be cleaned up
				SDK.XBL.XblSocialManagerDestroySocialUserGroup(m_owningUserGroupHandleFriends);
			}
			onAddUserOperationFinished.Invoke(gdkHResultOnCompletion);
		}

		//----------------------------------------------------------------------------
		private enum InternalRemoveCurrentUserParam { normalOperation, userHasSignedOut };
		//----------------------------------------------------------------------------
		private int InternalRemoveCurrentUser(InternalRemoveCurrentUserParam removeUserParam)
		{
			var removeLocalUserHResult = SDK.XBL.XblSocialManagerRemoveLocalUser(m_currentXUser.m_userHandle, XblSocialManagerExtraDetailLevel.NoExtraDetail);

			if ((InternalRemoveCurrentUserParam.userHasSignedOut == removeUserParam)
				&& (HR.E_INVALIDARG == removeLocalUserHResult))
			{
				Debug.Log($"user was already signed out! SDK.XBL.XblSocialManagerRemoveLocalUser returned: {PLHR.GetName(removeLocalUserHResult)} as expected, returning {PLHR.GetName(HR.S_OK)}");
				removeLocalUserHResult = HR.S_OK;
			}

			if (!HR.SUCCEEDED(removeLocalUserHResult))
				Debug.Log($"SDK.XBL.XblSocialManagerRemoveLocalUser error: {PLHR.GetName(removeLocalUserHResult)}");
			m_currentXUser = null;
			CurrentState = State.NoCurrentUser;

			return removeLocalUserHResult;
		}

		//----------------------------------------------------------------------------
		private const int k_iTempFriendXUIDListInitialSize = 128;
		private List<ulong> m_temp_XUIDList_Favourite = new List<ulong>(k_iTempFriendXUIDListInitialSize);
		private List<ulong> m_temp_XUIDList_MutualFollow = new List<ulong>(k_iTempFriendXUIDListInitialSize);
		private List<ulong> m_temp_XUIDList_Following = new List<ulong>(k_iTempFriendXUIDListInitialSize);
		//----------------------------------------------------------------------------
		private IEnumerator CO_HandleFetchFriendListForUser(GDK_XUserManager.XUserData xUserData, List<ulong> outListOfFriendXUIDs, Action<Int32> onFriendListOperationFinished)
		{
			outListOfFriendXUIDs.Clear();
			m_temp_XUIDList_Favourite.Clear();
			m_temp_XUIDList_MutualFollow.Clear();
			m_temp_XUIDList_Following.Clear();

			var coRoutineReturnHRValue = PLHR.Invalid;

			while (State.ProcessingAddUser == CurrentState)
			{
				yield return null;
			}

			var hr = SDK.XBL.XblSocialManagerUserGroupGetUsers(m_owningUserGroupHandleFriends, out var socialGroupUserArray);

			int numUsersInGroup = socialGroupUserArray?.Length ?? 0;

			Debug.Log($"SocialManager Group event - contains {numUsersInGroup} users");

			for (int i = 0; i < numUsersInGroup; ++i)
			{
				Debug.Log($"[{i}] {socialGroupUserArray[i].Gamertag} ({socialGroupUserArray[i].XboxUserId}) following us? {socialGroupUserArray[i].IsFollowingUser} favourite? {socialGroupUserArray[i].IsFavorite}");

				if (socialGroupUserArray[i].IsFavorite)
				{
					m_temp_XUIDList_Favourite.Add(socialGroupUserArray[i].XboxUserId);
				}
				else if (socialGroupUserArray[i].IsFollowingUser)
				{
					m_temp_XUIDList_MutualFollow.Add(socialGroupUserArray[i].XboxUserId);
				}
				else
				{
					Debug.Assert(socialGroupUserArray[i].IsFollowedByCaller, $"[{i}] {socialGroupUserArray[i].Gamertag} is not a favourite, mutual follow, OR followed by us!? should be impossible given filters");
					m_temp_XUIDList_Following.Add(socialGroupUserArray[i].XboxUserId);
				}
			}

			outListOfFriendXUIDs.AddRange(m_temp_XUIDList_Favourite);
			outListOfFriendXUIDs.AddRange(m_temp_XUIDList_MutualFollow);
			outListOfFriendXUIDs.AddRange(m_temp_XUIDList_Following);

			coRoutineReturnHRValue = HR.S_OK;

			Debug.Log($"completed: {PLHR.GetName(coRoutineReturnHRValue)}");
			onFriendListOperationFinished.Invoke(coRoutineReturnHRValue);
		}
#endif
	}
}