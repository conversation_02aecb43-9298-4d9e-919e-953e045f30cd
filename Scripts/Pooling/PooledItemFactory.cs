// Copyright Isto Inc.
using Isto.Core.Game;
using Isto.Core.Items;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UPool;
using Zenject;

namespace Isto.Core.Pooling
{
    /// <summary>
    /// This serves as a master factory for all types of pooled advanced item visuals so we don't have to get a separate factory for
    /// every type of item
    /// </summary>
    public class PooledItemFactory : MonoBehaviour
    {
        private static readonly string CORE_ITEM_ID = "CoreItem";

        private bool _initialized = false;
        private Dictionary<string, int> _poolRequests;
        private Dictionary<string, FlexiblePool<PoolableObject>> _pools;
        private Transform _templatesContainer;
        private int _poolsReady = 0;

        // Injected
        private MasterItemList _masterItemList; // Might be useful to iterate over this to generate small starting pools for all items
        private PoolingConfiguration _poolingConfiguration;

        [Inject]
        public void Inject(MasterItemList masterItemList, PoolingConfiguration poolingConfiguration)
        {
            _masterItemList = masterItemList;
            _poolingConfiguration = poolingConfiguration;
        }

        public void Awake()
        {
            GameState.PoolsInitialized = false;
            Initialize();
        }

        public GameObject GetPooledCoreItem()
        {
            if (!_initialized || _pools == null)
            {
                Debug.LogError($"Trying to get CoreItem from PooledItemFactory while not initialized", this.gameObject);
                return null;
            }

            if (!_pools.ContainsKey(CORE_ITEM_ID))
            {
                Debug.LogError($"CoreItem does not have a pool configured for it");
                return null;
            }

#if POOL_LOGGING
            Debug.Log($"PooledItemFactory.GetPooledCoreItem");
#endif

            PoolableObject poolItem = _pools[CORE_ITEM_ID].Acquire();
            return poolItem.gameObject;
        }

        public void Request(string itemID)
        {
            if (_poolRequests == null)
            {
                _poolRequests = new Dictionary<string, int>();
            }

            if (!_poolRequests.ContainsKey(itemID))
            {
                _poolRequests.Add(itemID, 0);
            }

            _poolRequests[itemID]++;
        }

        private int GetRequestCount(Item itemDef)
        {
            if (_poolRequests == null)
            {
                return 0;
            }

            if (!_poolRequests.ContainsKey(itemDef.itemID))
            {
                return 0;
            }

            return _poolRequests[itemDef.itemID];
        }

        public GameObject GetPooledItem(Item itemDef)
        {
            if (!_initialized || _pools == null)
            {
                Debug.LogError($"Trying to get item {itemDef.itemID} from PooledItemFactory while not initialized", this.gameObject);
                return null;
            }

            if (!_pools.ContainsKey(itemDef.itemID))
            {
                // If pooled items do not exist the normal spawning flow will happen
                //Debug.LogWarning($"Asset {itemDef.itemID} does not have a pool configured for it");
                return null;
            }

#if POOL_LOGGING
            Debug.Log($"PooledItemFactory.GetPooledItem of {itemDef.itemID}");
#endif

            PoolableObject poolItem = _pools[itemDef.itemID].Acquire();
            return poolItem.gameObject;
        }

        public bool IsCoreItemInsidePool(PoolableObject item)
        {
            if (item.IsNullOrDestroyed())
            {
                Debug.LogError($"Should not be testing null or destroyed game objects against the core item pool", this.gameObject);
                return false;
            }

            if (!_pools.ContainsKey(CORE_ITEM_ID))
            {
                Debug.LogWarning($"PooledItemFactory.IsCoreItemInsidePool called on item {item.gameObject.name} but core item pool is not available", item.gameObject);
                return false;
            }

            Transform pool = _pools[CORE_ITEM_ID].Container;
            return pool != null && item.transform.parent == pool;
        }

        public bool IsInsidePool(PooledInteractableItem item)
        {
            string itemID = item.ItemID;
            if (itemID == null || !_pools.ContainsKey(itemID))
                return false;

            Transform pool = _pools[itemID].Container;
            return pool != null && item.transform.parent == pool;
        }

        public void ReturnPooledCoreItem(PoolableObject item)
        {
            if (!_initialized || _pools == null)
            {
                Debug.LogError($"Trying to return CoreItem {item.gameObject.name} to PooledItemFactory but the pools are not initialized", this.gameObject);
            }

#if POOL_LOGGING
            Debug.Log($"PooledItemFactory.ReturnPooledCoreItem on {item.gameObject.name}", item.gameObject);
#endif

            IActionOnRecycle[] recyclables = item.GetComponentsInChildren<IActionOnRecycle>();
            for (int i = 0; i < recyclables.Length; i++)
            {
                recyclables[i].OnRecycle();
            }

            _pools[CORE_ITEM_ID].Recycle(item);
        }

        public void ReturnPooledAdvancedItem(PooledInteractableItem item)
        {
            if (!_initialized || _pools == null)
            {
                Debug.LogError($"Trying to return item {item.gameObject.name} to PooledItemFactory but the pools are not initialized", this.gameObject);
            }

            if (item.PoolingComponent == null)
            {
                Debug.LogError($"Trying to return to PooledItemFactory item {item.gameObject.name} but it has no PoolableObject", this.gameObject);
            }

            if (!item.IsPooled)
            {
                Debug.LogError($"Trying to return to PooledItemFactory the PooledInteractableItem {item.gameObject.name} but it is not from a pool", this.gameObject);
            }

#if POOL_LOGGING
            Debug.Log($"PooledItemFactory.ReturnPooledItem on {item.name}");
#endif

            IActionOnRecycle[] recyclables = item.GetComponentsInChildren<IActionOnRecycle>();
            for (int i = 0; i < recyclables.Length; i++)
            {
                recyclables[i].OnRecycle();
            }

            string itemID = item.ItemID;

            _pools[itemID].Recycle(item.PoolingComponent);
        }

        public bool IsItemPooled(Item item)
        {
            for (int i = 0; i < _poolingConfiguration.ItemPools.Count; i++)
            {
                if (_poolingConfiguration.ItemPools[i].item == item)
                    return true;
            }

            return false;
        }

        // At the moment this method is meant to be used for testing only and not in game. No guarantees
        public void ResizePool(string itemId, int size)
        {
            if (!_initialized || _pools == null)
            {
                Debug.LogError($"Trying to resize {itemId} pool but the pools are not initialized", this.gameObject);
                return;
            }

            PoolingConfiguration.PooledItemConfiguration poolConfig = _poolingConfiguration.ItemPools.Where(x => x.item.itemID == itemId).FirstOrDefault();
            if (poolConfig == null)
            {
                Debug.LogError($"Trying to resize {itemId} pool but config was not found for it", this.gameObject);
                return;
            }

            if (!_pools.ContainsKey(itemId))
            {
                Debug.LogError($"Trying to resize {itemId} pool but that pool does not exist", this.gameObject);
                return;
            }

            GameState.PoolsInitialized = false;

            FlexiblePool<PoolableObject> pool = _pools[itemId];
            pool.DestroyAndDeallocateAll();
            _pools.Remove(itemId);

            PreLoad(poolConfig.item, size);
        }

        private void Initialize()
        {
            if (_initialized)
                return;

            if (_pools == null)
            {
                _pools = new Dictionary<string, FlexiblePool<PoolableObject>>();
                _templatesContainer = new GameObject("PooledItemPrefabs").transform;
                _templatesContainer.parent = this.transform;
            }

            PreLoadCoreItemPool();

            foreach (PoolingConfiguration.PooledItemConfiguration poolConfig in _poolingConfiguration.ItemPools)
            {
                PreLoad(poolConfig.item);
            }

            // I would like to put together a small default pool (1 or 2 instances) of any placeable item instead of needing to have
            // all of them in the pooling config. For which I imagine iterating over the MasterItemList is a good start.
            // But if we do that we'll need to find and fix any remaining code for creating/destroying items.
            // Note: Some advanced items are not placeable and can't be pooled, e.g. makeshift torch. Can we check if they are
            // configured to "simply activate" and skip those? Would the rest be good?
            // Also note that some items in MasterItemList have bad configurations, but I think this means we should remove them
            // from there. For instance TallDoor - if it's not in the game, it probably should not be in the list, especially since
            // the GetItem cheat pulls anything that's referenced in that list for you.
            /*foreach (AdvancedItem item in _masterItemList.GetAllAdvancedItems())
            {
                PreLoad(item);
            }*/

            _initialized = true;
        }

        private void PreLoadCoreItemPool()
        {
            Addressables.LoadResourceLocationsAsync(_poolingConfiguration.coreItemAddressable).Completed += op =>
            {
                if (op.Result != null && op.Result.Count > 0)
                {
                    _poolingConfiguration.coreItemAddressable.InstantiateAsync(Vector3.up * 1000f, Quaternion.identity, _templatesContainer).Completed += op =>
                    {
                        if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                        {
                            GameObject createdItem = op.Result;
                            createdItem.SetActive(false);

                            int poolSize = _poolingConfiguration.coreItemPoolStartingAmount;
                            Transform pooledItemsContainer = new GameObject(CORE_ITEM_ID + "Pool").transform;
                            pooledItemsContainer.parent = this.transform;
                            _pools.Add(CORE_ITEM_ID, new FlexiblePool<PoolableObject>(poolSize, createdItem, pooledItemsContainer, hideContainerInHierarchy: false));

                            // Number the items for easier flow debugging
                            createdItem.gameObject.name = createdItem.gameObject.name + _pools[CORE_ITEM_ID].Size;

                            OnPoolInitializationDone();
                        }
                    };
                }
                else
                {
                    Debug.LogError($"Cannot find key for core item addressable asset");
                }
            };
        }

        private void PreLoad(Item itemDef, int sizeOverride = -1)
        {
            if (string.IsNullOrEmpty(itemDef.addressableAsset?.AssetGUID))
            {
                Debug.LogError($"Advanced item {itemDef.itemID} has failed to preload into PooledItemFactory because it has no valid addressable");
                return;
            }

            if (_pools.ContainsKey(itemDef.itemID))
            {
                return;
            }

            Addressables.LoadResourceLocationsAsync(itemDef.addressableAsset).Completed += op =>
            {
                if (op.Result != null && op.Result.Count > 0)
                {
                    itemDef.addressableAsset.InstantiateAsync(Vector3.up * 1000f, Quaternion.identity, _templatesContainer).Completed += op =>
                    {
                        if (op.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                        {
                            GameObject createdItem = op.Result;
                            createdItem.SetActive(false);

                            PooledInteractableItem poolable = createdItem.GetComponent<PooledInteractableItem>();
                            if (poolable == null)
                            {
                                Debug.LogWarning($"Trying to preload a pool for item {itemDef.itemID} but item is not a PooledInteractableItem");
                                OnPoolInitializationDone();
                                return;
                            }

                            int poolSize = 2;

                            PoolingConfiguration.PooledItemConfiguration poolConfig = _poolingConfiguration.ItemPools.Where(x => x.item.itemID == itemDef.itemID).FirstOrDefault();
                            if (poolConfig == null)
                            {
                                Debug.LogWarning($"Poolable item has no configuration data in asset {_poolingConfiguration.name}", _poolingConfiguration);
                            }
                            else
                            {
                                poolSize = poolConfig.startingAmount;
                            }

                            this.WaitForConditionIfNeeded(() => !GameState.WaitingForSaveFileLoad,
                                    () =>
                                    {
                                        int requestedDuringLoading = GetRequestCount(itemDef);
                                        poolSize += requestedDuringLoading;

                                        if (sizeOverride >= 0)
                                            poolSize = sizeOverride;

                                        Transform pooledItemsContainer = new GameObject(itemDef.itemID + "Pool").transform;
                                        pooledItemsContainer.parent = this.transform;
                                        _pools.Add(itemDef.itemID, new FlexiblePool<PoolableObject>(poolSize, createdItem, pooledItemsContainer, hideContainerInHierarchy: false));

                                        // Number the items for easier flow debugging
                                        createdItem.gameObject.name = createdItem.gameObject.name + _pools[itemDef.itemID].Size;

                                        OnPoolInitializationDone();
                                    });
                        }
                    };
                }
                else
                {
                    Debug.LogError($"Cannot find key {itemDef.addressableAsset.RuntimeKey}");
                }
            };
        }

        private void OnPoolInitializationDone()
        {
            _poolsReady++;

            // need one pool for earch advanced item and one for core items
            if (_poolsReady >= _poolingConfiguration.ItemPools.Count + 1)
            {
                Debug.Log("PooledItemFactory: all pools have been initialized");
                GameState.PoolsInitialized = true;
            }
        }

        [ContextMenu("Log All Pools")]
        private void LogAllPools()
        {
            if (_pools == null)
            {
                Debug.Log($"Pools do not exist");
                return;
            }

            Debug.Log($"===POOL=OF=POOLS===");
            foreach (KeyValuePair<string, FlexiblePool<PoolableObject>> pair in _pools)
            {
                Debug.Log($"[{pair.Key}] SIZE: {pair.Value.Size} AVAIL:{pair.Value.AvailableItems}");
            }
        }
    }
}