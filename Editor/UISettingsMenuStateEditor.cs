// Copyright Isto Inc.

# if UNITY_EDITOR
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomEditor(typeof(UISettingsMenuState))]
    public class UISettingsMenuStateEditor : Editor
    {
        // OTHER FIELDS
        
        private List<CanvasGroup> _canvasGroups;
        private CanvasGroup _settingsCanvas;
        private CanvasGroup _gameplayCanvas;
        private CanvasGroup _audioCanvas;
        private CanvasGroup _videoCanvas;
        private CanvasGroup _controlsCanvas;
        
        private bool _foldout = true;
        private bool _subFoldout = true;

        
        // PROPERTIES

        private UISettingsMenuState MenuState => (UISettingsMenuState)target;
        
        
        // LIFECYCLE EVENTS
        
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector(); // for other non-HideInInspector fields

            _foldout = EditorGUILayout.Foldout(_foldout, "Manage Alphas", true);

            if (_foldout)
            {
                EditorGUI.indentLevel++;
                _subFoldout = EditorGUILayout.Foldout(_subFoldout, "Manage Canvas Groups", true);
                if (_subFoldout)
                {
                    FetchCanvasObjects();
                }

                CreateButton("Show Root Background", _settingsCanvas);
                CreateButton("Show Gameplay Settings", _gameplayCanvas, _settingsCanvas);
                CreateButton("Show Video Settings", _videoCanvas, _settingsCanvas);
                CreateButton("Show Audio Settings", _audioCanvas, _settingsCanvas);
                CreateButton("Show Control Settings", _controlsCanvas, _settingsCanvas);

                if (GUILayout.Button("Reset to Default"))
                {
                    ResetToDefaults();
                }

                EditorGUI.indentLevel--;
            }
        }

        private void FetchCanvasObjects()
        {
            _settingsCanvas = FetchCanvasObject("Root Settings", MenuState._settingsCanvas);
            _gameplayCanvas = FetchCanvasObject("Gameplay Settings", MenuState._gameplayCanvas);
            _videoCanvas = FetchCanvasObject("Video Settings", MenuState._videoCanvas);
            _audioCanvas = FetchCanvasObject("Audio Settings", MenuState._audioCanvas);
            _controlsCanvas = FetchCanvasObject("Control Settings", MenuState._controlsCanvas);

            _canvasGroups = new List<CanvasGroup>
            {
                _settingsCanvas,
                _gameplayCanvas,
                _videoCanvas,
                _audioCanvas,
                _controlsCanvas
            };
        }

        private CanvasGroup FetchCanvasObject(string label, CanvasGroup group)
        {
            return EditorGUILayout.ObjectField(label, group, typeof(CanvasGroup), true) as CanvasGroup;
        }

        private void CreateButton(string buttonName, params CanvasGroup[] canvasGroups)
        {
            if (GUILayout.Button(buttonName))
            {
                ResetToDefaults();
                foreach (CanvasGroup group in canvasGroups)
                {
                    SetAlpha(group, 1f);
                }
            }
        }

        private void ResetToDefaults()
        {
            foreach (CanvasGroup canvasGroup in _canvasGroups)
            {
                SetAlpha(canvasGroup, 0f);
            }
        }

        private void SetAlpha(CanvasGroup canvasGroup, float alpha)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = alpha;
                EditorUtility.SetDirty(canvasGroup.gameObject);
            }
        }
    }
}
#endif