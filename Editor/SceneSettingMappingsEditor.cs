// Copyright Isto Inc.
using Isto.Core.Configuration;
using System.Collections.Generic;
using UnityEditor;

namespace Isto.Core
{
    [CustomEditor(typeof(SceneSettingMappings))]
    public class SceneSettingMappingsEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            SceneSettingMappings settings = target as SceneSettingMappings;
            var settingsMappings = settings.settingsMappings;

            // Do Custom Editor logic here
            HashSet<GameModeDefinition> normalBuildScenePairs = new HashSet<GameModeDefinition>();

            for (int i = 0; i < settingsMappings.Count; i++)
            {
                if (!settingsMappings[i].sandboxUseOnly)
                {
                    if (normalBuildScenePairs.Contains(settingsMappings[i].gameMode))
                    {
                        EditorGUILayout.HelpBox("\n\nDuplicate Values in SettingsMappings not supported. Fix this by setting sandbox flag or removing duplicate\n\n", MessageType.Error);
                    }
                    else
                        normalBuildScenePairs.Add(settingsMappings[i].gameMode);
                }
            }

            base.OnInspectorGUI();
        }
    }
}