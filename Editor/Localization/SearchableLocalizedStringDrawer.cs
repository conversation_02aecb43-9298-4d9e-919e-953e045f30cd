// Copyright Isto Inc.

using System;
using System.Collections.Generic;
using I2.Loc;
using UnityEditor;
using UnityEngine;

namespace Isto.Core.Localization
{
    /// <summary>
    /// Custom property drawer for LocalizedString that adds a search field to make it easier to find terms.
    /// This is applied to fields with the [SearchableLocalizedString] attribute.
    /// </summary>
    [CustomPropertyDrawer(typeof(SearchableLocalizedStringAttribute))]
    public class SearchableLocalizedStringDrawer : PropertyDrawer
    {
        // OTHER FIELDS

        private static readonly int MAX_VISIBLE_SUGGESTIONS = 5;
        private static readonly List<string> _filteredSuggestions = new List<string>();

        private string _previousFilter = "";
        private string _searchText = "";
        private int _selectedSuggestionIndex = -1;
        private bool _showSearchField;
        private bool _showSuggestions;
        private Rect _suggestionRect;
        private GUIContent[] _contextTerms;

        private int _framesLeftBeforeUpdate;


        // LIFECYCLE EVENTS

        public override void OnGUI(Rect rect, SerializedProperty property, GUIContent label)
        {
            // Adjust the main rect if search field is visible
            Rect mainRect = rect;
            if (_showSearchField)
            {
                mainRect.y += EditorGUIUtility.singleLineHeight;
                mainRect.height -= EditorGUIUtility.singleLineHeight;

                if (_showSuggestions && _filteredSuggestions.Count > 0)
                {
                    int suggestionCount = Mathf.Min(_filteredSuggestions.Count, MAX_VISIBLE_SUGGESTIONS);
                    mainRect.y += suggestionCount * EditorGUIUtility.singleLineHeight;
                    mainRect.height -= suggestionCount * EditorGUIUtility.singleLineHeight;
                }
            }

            // Search field area
            Rect searchRect = rect;
            searchRect.height = EditorGUIUtility.singleLineHeight;

            // Draw search toggle and field
            Rect searchToggleRect = searchRect;
            searchToggleRect.width = 20;

            _showSearchField = EditorGUI.Toggle(searchToggleRect, _showSearchField, EditorStyles.foldout);

            if (_showSearchField)
            {
                Rect searchFieldRect = searchRect;
                searchFieldRect.x += 20;
                searchFieldRect.width -= 20;

                // Handle keyboard events for navigation
                Event currentEvent = Event.current;
                bool keyPressed = false;

                if (_showSuggestions && _filteredSuggestions.Count > 0)
                {
                    if (currentEvent.type == EventType.KeyDown)
                    {
                        if (currentEvent.keyCode == KeyCode.DownArrow)
                        {
                            _selectedSuggestionIndex = Mathf.Min(_selectedSuggestionIndex + 1, _filteredSuggestions.Count - 1);
                            keyPressed = true;
                            currentEvent.Use();
                        }
                        else if (currentEvent.keyCode == KeyCode.UpArrow)
                        {
                            _selectedSuggestionIndex = Mathf.Max(_selectedSuggestionIndex - 1, -1);
                            keyPressed = true;
                            currentEvent.Use();
                        }
                        else if (currentEvent.keyCode == KeyCode.Return || currentEvent.keyCode == KeyCode.KeypadEnter)
                        {
                            if (_selectedSuggestionIndex >= 0 && _selectedSuggestionIndex < _filteredSuggestions.Count)
                            {
                                // Apply the selected suggestion
                                _searchText = _filteredSuggestions[_selectedSuggestionIndex];
                                _showSuggestions = false;
                                keyPressed = true;
                                currentEvent.Use();

                                // Apply the term directly
                                SerializedProperty selectedTermProp = property.FindPropertyRelative("mTerm");
                                selectedTermProp.stringValue = _searchText;
                                property.serializedObject.ApplyModifiedProperties();

                                // Force repaint to update the dropdown
                                EditorUtility.SetDirty(property.serializedObject.targetObject);
                                GUI.changed = true;
                            }
                        }
                        else if (currentEvent.keyCode == KeyCode.Escape)
                        {
                            _showSuggestions = false;
                            keyPressed = true;
                            currentEvent.Use();
                        }
                    }
                }

                EditorGUI.BeginChangeCheck();
                _searchText = EditorGUI.TextField(searchFieldRect, _searchText);
                bool textChanged = EditorGUI.EndChangeCheck();

                if (textChanged || keyPressed)
                {
                    // Reset the terms cache to force an update with the new filter
                    _framesLeftBeforeUpdate = 0;
                    _previousFilter = null;

                    // Update suggestions
                    UpdateSuggestions();
                    _showSuggestions = !string.IsNullOrEmpty(_searchText) && _filteredSuggestions.Count > 0;
                    _selectedSuggestionIndex = -1;

                    // Set up suggestion rect
                    if (_showSuggestions)
                    {
                        _suggestionRect = new Rect(
                            searchFieldRect.x,
                            searchFieldRect.y + searchFieldRect.height,
                            searchFieldRect.width,
                            Mathf.Min(_filteredSuggestions.Count, MAX_VISIBLE_SUGGESTIONS) * EditorGUIUtility.singleLineHeight
                        );
                    }
                }

                // Draw suggestions dropdown
                if (_showSuggestions && _filteredSuggestions.Count > 0)
                {
                    DrawSuggestions(property);
                }
            }
            else
            {
                _showSuggestions = false;
            }

            // Term selection
            Rect termRect = mainRect;
            termRect.xMax -= 50;
            SerializedProperty termProp = property.FindPropertyRelative("mTerm");  // I2.Loc property name, can't change
            ShowGUICached(termRect, termProp, label, null, _searchText, ref _contextTerms, ref _framesLeftBeforeUpdate, ref _previousFilter);

            // Mask field
            Rect maskRect = mainRect;
            maskRect.xMin = maskRect.xMax - 30;
            SerializedProperty termIgnoreRTL = property.FindPropertyRelative("mRTL_IgnoreArabicFix");  // I2.Loc property name, can't change
            SerializedProperty termConvertNumbers = property.FindPropertyRelative("mRTL_ConvertNumbers");  // I2.Loc property name, can't change
            SerializedProperty termDontLocalizeParams = property.FindPropertyRelative("m_DontLocalizeParameters");  // I2.Loc property name, can't change
            int mask = (termIgnoreRTL.boolValue ? 0 : 1) +
                       (termConvertNumbers.boolValue ? 0 : 2) +
                       (termDontLocalizeParams.boolValue ? 0 : 4);

            int newMask = EditorGUI.MaskField(maskRect, mask, new[] { "Arabic Fix", "Ignore Numbers in RTL", "Localize Parameters" });
            if (newMask != mask)
            {
                termIgnoreRTL.boolValue = (newMask & 1) == 0;
                termConvertNumbers.boolValue = (newMask & 2) == 0;
                termDontLocalizeParams.boolValue = (newMask & 4) == 0;
            }

            Rect showRect = mainRect;
            showRect.xMin = termRect.xMax;
            showRect.xMax = maskRect.xMin;
            bool enabled = GUI.enabled;
            GUI.enabled = enabled & (!string.IsNullOrEmpty(termProp.stringValue) && termProp.stringValue != "-");
            if (GUI.Button(showRect, "?"))
            {
                LanguageSourceData source = LocalizationManager.GetSourceContaining(termProp.stringValue);
                LocalizationEditor.mKeyToExplore = termProp.stringValue;  // I2.Loc property name, can't change
                Selection.activeObject = source.ownerObject;
            }
            GUI.enabled = enabled;
        }


        // ACCESSORS

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            float height = base.GetPropertyHeight(property, label);

            // Add extra height if search field is visible
            if (_showSearchField)
            {
                height += EditorGUIUtility.singleLineHeight;

                // Add extra height for suggestions dropdown if visible
                if (_showSuggestions && _filteredSuggestions.Count > 0)
                {
                    int suggestionCount = Mathf.Min(_filteredSuggestions.Count, MAX_VISIBLE_SUGGESTIONS);
                    height += suggestionCount * EditorGUIUtility.singleLineHeight;
                }
            }

            return height;
        }

        private int GetTermIndex(GUIContent[] terms_Contexts, string term)
        {
            for (int i = 0; i < terms_Contexts.Length; ++i)
                if (terms_Contexts[i].text == term)
                    return i;
            return -1;
        }


        // OTHER METHODS

        private void DrawSuggestions(SerializedProperty property)
        {
            GUI.Box(_suggestionRect, GUIContent.none, EditorStyles.helpBox);

            // Draw the suggestions
            int visibleCount = Mathf.Min(_filteredSuggestions.Count, MAX_VISIBLE_SUGGESTIONS);
            for (int i = 0; i < visibleCount; i++)
            {
                Rect itemRect = new Rect(
                    _suggestionRect.x,
                    _suggestionRect.y + i * EditorGUIUtility.singleLineHeight,
                    _suggestionRect.width,
                    EditorGUIUtility.singleLineHeight
                );

                // Highlight the selected suggestion
                if (i == _selectedSuggestionIndex)
                {
                    EditorGUI.DrawRect(itemRect, new Color(0.2f, 0.4f, 0.6f, 0.5f));
                }

                // Check if mouse is over this item
                bool mouseOver = itemRect.Contains(Event.current.mousePosition);
                if (mouseOver)
                {
                    EditorGUI.DrawRect(itemRect, new Color(0.8f, 0.8f, 0.8f, 0.1f));

                    // Handle mouse click
                    if (Event.current.type == EventType.MouseDown && Event.current.button == 0)
                    {
                        // Apply the selected suggestion
                        _searchText = _filteredSuggestions[i];
                        _showSuggestions = false;
                        Event.current.Use();

                        // Apply the term directly
                        SerializedProperty clickedTermProp = property.FindPropertyRelative("mTerm");
                        clickedTermProp.stringValue = _searchText;
                        property.serializedObject.ApplyModifiedProperties();

                        // Force repaint to update the dropdown
                        EditorUtility.SetDirty(property.serializedObject.targetObject);
                        GUI.changed = true;
                    }
                }

                // Draw the suggestion text with highlighted matching part
                string suggestion = _filteredSuggestions[i];
                GUIStyle style = new GUIStyle(EditorStyles.label);
                style.normal.textColor = mouseOver || i == _selectedSuggestionIndex ? Color.white : EditorStyles.label.normal.textColor;
                style.richText = true;

                // Highlight the matching part
                string displayText = HighlightMatchingPart(suggestion, _searchText);

                EditorGUI.LabelField(itemRect, displayText, style);
            }
        }

        // The following methods are copied from TermsPopup_Drawer to make this drawer self-contained
        private bool ShowGUICached(Rect position, SerializedProperty property, GUIContent label, LanguageSourceData source, string filter, ref GUIContent[] terms_Contexts, ref int framesBeforeUpdating, ref string prevFilter)
        {
            UpdateTermsCache(source, filter, ref terms_Contexts, ref framesBeforeUpdating, ref prevFilter);

            label = EditorGUI.BeginProperty(position, label, property);

            EditorGUI.BeginChangeCheck();

            int index = property.stringValue == "-" || property.stringValue == "" ? terms_Contexts.Length - 1 :
                property.stringValue == " " ? terms_Contexts.Length - 2 :
                GetTermIndex(terms_Contexts, property.stringValue);
            int newIndex = EditorGUI.Popup(position, label, index, terms_Contexts);

            if (EditorGUI.EndChangeCheck())
            {
                property.stringValue = newIndex < 0 || newIndex == terms_Contexts.Length - 1 ? string.Empty : terms_Contexts[newIndex].text;
                if (newIndex == terms_Contexts.Length - 1)
                    property.stringValue = "-";
                else
                if (newIndex < 0 || newIndex == terms_Contexts.Length - 2)
                    property.stringValue = string.Empty;
                else
                    property.stringValue = terms_Contexts[newIndex].text;

                EditorGUI.EndProperty();
                return true;
            }

            EditorGUI.EndProperty();
            return false;
        }

        private void UpdateTermsCache(LanguageSourceData source, string filter, ref GUIContent[] terms_Contexts, ref int framesBeforeUpdating, ref string prevFilter)
        {
            framesBeforeUpdating--;
            if (terms_Contexts != null && framesBeforeUpdating > 0 && filter == prevFilter)
            {
                return;
            }
            framesBeforeUpdating = 60;
            prevFilter = filter;

            List<string> Terms = source == null ? LocalizationManager.GetTermsList() : source.GetTermsList();

            if (!string.IsNullOrEmpty(filter))
            {
                Terms = Filter(Terms, filter);
            }

            Terms.Sort(StringComparer.OrdinalIgnoreCase);
            Terms.Add("");
            Terms.Add("<inferred from text>");
            Terms.Add("<none>");

            terms_Contexts = DisplayOptions(Terms);
        }

        private List<string> Filter(List<string> terms, string filter)
        {
            if (string.IsNullOrEmpty(filter))
                return terms;

            List<string> filtered = new List<string>();
            string lowerFilter = filter.ToLowerInvariant();

            for (int i = 0; i < terms.Count; i++)
            {
                string term = terms[i];
                if (term.ToLowerInvariant().Contains(lowerFilter))
                {
                    filtered.Add(term);
                }
            }

            return filtered;
        }

        private GUIContent[] DisplayOptions(IList<string> terms)
        {
            GUIContent[] options = new GUIContent[terms.Count];
            for (int i = 0; i < terms.Count; i++)
            {
                options[i] = new GUIContent(terms[i]);
            }

            return options;
        }

        private void UpdateSuggestions()
        {
            if (string.IsNullOrEmpty(_searchText))
            {
                _filteredSuggestions.Clear();
                return;
            }

            List<string> allTerms = LocalizationManager.GetTermsList();
            _filteredSuggestions.Clear();

            string lowerFilter = _searchText.ToLowerInvariant();
            foreach (string term in allTerms)
            {
                if (term.ToLowerInvariant().Contains(lowerFilter))
                {
                    _filteredSuggestions.Add(term);

                    // Limit the number of suggestions to avoid performance issues
                    if (_filteredSuggestions.Count >= 100)
                        break;
                }
            }

            // Sort suggestions by relevance (exact matches first, then starts with, then contains)
            _filteredSuggestions.Sort((a, b) => {
                bool aExact = a.Equals(_searchText, StringComparison.OrdinalIgnoreCase);
                bool bExact = b.Equals(_searchText, StringComparison.OrdinalIgnoreCase);

                if (aExact && !bExact) return -1;
                if (!aExact && bExact) return 1;

                bool aStartsWith = a.StartsWith(_searchText, StringComparison.OrdinalIgnoreCase);
                bool bStartsWith = b.StartsWith(_searchText, StringComparison.OrdinalIgnoreCase);

                if (aStartsWith && !bStartsWith) return -1;
                if (!aStartsWith && bStartsWith) return 1;

                return a.CompareTo(b);
            });
        }

        private string HighlightMatchingPart(string text, string searchText)
        {
            string result = text;

            if (!string.IsNullOrEmpty(searchText))
            {
                int index = text.IndexOf(searchText, StringComparison.OrdinalIgnoreCase);
                if (index >= 0)
                {
                    string before = text.Substring(0, index);
                    string match = text.Substring(index, searchText.Length);
                    string after = text.Substring(index + searchText.Length);

                    // Use different highlight colors for light and dark themes
                    string highlightColor = EditorGUIUtility.isProSkin ? "#FFA500" : "#0000FF";
                    result = before + "<color=" + highlightColor + ">" + match + "</color>" + after;
                }
            }

            return result;
        }
    }
}