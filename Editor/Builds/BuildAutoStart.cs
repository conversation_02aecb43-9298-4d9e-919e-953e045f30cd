// Copyright Isto Inc.
using System.Collections;
using UnityEditor;
using UnityEngine;

namespace Isto.Core
{
    [CreateAssetMenu(fileName = "New Build Auto-Start Step", menuName = "Scriptables/Builds/BuildSteps/BuildAutoStart")]
    public class BuildAutoStart : BuildStep
    {
        public bool isDevelopment = false;
        public string buildOutputPath = "";

        public override string DisplayName => "Auto-Start Unity Build (should be last step)";
        public override string StartMessage => "Starting Build";
        public override string EndMessage => "Build Finished";

        public override IEnumerator InternalOperations()
        {
            if (scenes.Count != 0)
            {
                Debug.LogWarning($"BuildAutoStart step has scenes assigned to it but will not use them when "
                               + $"starting build.", this);
            }

            BuildPlayerOptions options = new BuildPlayerOptions();

            // These will default to what's in the build settings window but we could also assign them.
            //options.scenes = null;

            // Seems to default to None even if we try to get the options so might as well be explicit
            options.options = BuildOptions.None;

            if (!string.IsNullOrEmpty(buildOutputPath))
            {
                // If we don't assign this, there will be a popup window asking you to choose a path.
                options.locationPathName = buildOutputPath;

                options.targetGroup = EditorUserBuildSettings.selectedBuildTargetGroup;
                options.target = EditorUserBuildSettings.activeBuildTarget;
            }
            else
            {
                // This will prompt the player to choose a output path for the build.
                // It will also default the target and target group to what's in the build settings window.
                options = BuildPlayerWindow.DefaultBuildMethods.GetBuildPlayerOptions(options);
            }

            if (isDevelopment)
            {
                options.options |= BuildOptions.Development;
            }

            // Haven't really looked into these yet since we don't need them, currently we change the defines directly
            // in the player settings during the pre-build steps.
            //options.extraScriptingDefines = new string[] { null };

            BuildPlayerWindow.DefaultBuildMethods.BuildPlayer(options);

            AddProgress(WorkUnits);

            // TODO: consider opening explorer at the build path like unity does

            DeclareEnd();

            yield break;
        }

        public override void Abort()
        {

        }
    }
}