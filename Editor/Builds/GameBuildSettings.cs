// Copyright Isto Inc.

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core
{
    [CreateAssetMenu(fileName = ASSET_NAME, menuName = "Scriptables/Builds/GameBuildSettings")]
    public class GameBuildSettings : ScriptableObject
    {
        private const string ASSET_NAME = "GameBuildSettings";

        [Header("Scenes In Builds:")]
        public List<SceneAsset> mainGameScenes;
        [Header("Scenes In Builds (Addressables):")]
        public List<SceneAsset> mainGameAddressableScenes;
        [FormerlySerializedAs("playModetestScenes")]
        public List<SceneAsset> playModeTestScenes;

        [Header("Custom Build Symbols:")]
        public List<BuildSymbol> customSymbols;

        [Header("Build Steps:")]
        public List<BuildStep> customBuildSteps;

        [Header("Build Targets:")]
        public List<BuildTarget> buildTargets;

        [Header("Build Hooks:")]
        public bool ignoreBuildHooks = false; // just so you can easily disable them
        public List<BuildHook> buildHooks;

        public static GameBuildSettings LoadFromResources()
        {
            // TODO: use AssetDatabase.LoadAssetAtPath instead
            GameBuildSettings settings = Resources.Load<GameBuildSettings>(ASSET_NAME);

            return settings;
        }

        public List<SceneAsset> GetAllScenesForAutomatedTesting()
        {
            List<SceneAsset> scenesForTesting = new List<SceneAsset>(playModeTestScenes);
            scenesForTesting.AddRange(mainGameScenes);
            return scenesForTesting;
        }
    }
}