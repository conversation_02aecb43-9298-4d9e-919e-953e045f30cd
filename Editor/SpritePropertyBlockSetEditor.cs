// Copyright Isto Inc.
using UnityEditor;
using UnityEngine;

namespace Isto.Core
{
    [CustomEditor(typeof(CoreSpritePropertyBlockSet))]
    [CanEditMultipleObjects]
    public class SpritePropertyBlockSetEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            EditorGUI.BeginChangeCheck();

            base.OnInspectorGUI();

            if (EditorGUI.EndChangeCheck() || GUILayout.Button("Refresh This Item"))
            {
                // IF something changed, update the material and reflection object
                for (int i = 0; i < targets.Length; i++)
                {
                    CoreSpritePropertyBlockSet propertySet = targets[i] as CoreSpritePropertyBlockSet;

                    if (propertySet != null)
                    {
                        propertySet.AddReflection();
                        propertySet.SetPropertyBlockInEditor();
                    }
                }
            }
        }
    }
}