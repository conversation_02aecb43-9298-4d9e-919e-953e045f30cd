// Copyright Isto Inc.
using Isto.Core.Items;
using UnityEditor;

namespace Isto.Core
{
    [CustomEditor(typeof(TileSpriteSpawnRules))]
    public class TileSpriteSpawnRulesEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            EditorGUI.BeginChangeCheck();

            base.OnInspectorGUI();

            //TileSpriteSpawnRules rules = target as TileSpriteSpawnRules;

            //SerializedProperty list = serializedObject.FindProperty("validResourceSpawnSprites");

            //EditorGUILayout.PropertyField(list, false);

            //EditorGUI.indentLevel += 1;

            //for (int i = 0; i < list.arraySize; i++)
            //{
            //    EditorGUILayout.PropertyField(list.GetArrayElementAtIndex(i));
            //}

            //EditorGUI.indentLevel -= 1;

            //for (int i = 0; i < rules.validResourceSpawnSprites.Count; i++)
            //{
            //    Sprite spr = rules.validResourceSpawnSprites[i];
            //    EditorGUILayout.ObjectField(spr.name, spr, typeof(Sprite), false);
            //}

            if (EditorGUI.EndChangeCheck())
            {
                // Do Custom Editor logic here

            }
        }
    }
}