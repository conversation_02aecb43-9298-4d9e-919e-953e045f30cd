// unset
using UnityEditor;
using UnityEngine;

namespace Isto.Core
{
    [CustomEditor(typeof(EditorRecorder))]
    public class EditorRecorderEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            EditorRecorder component = target as EditorRecorder;
            
            base.OnInspectorGUI();
            
            if (GUILayout.Button("Open Editor Recorder Window"))
            {
                if (!EditorWindow.HasOpenInstances<EditorRecorderWindow>())
                {
                    EditorRecorderWindow.OpenWindow(component);
                }
            }

            Color defaultGUIColor = GUI.color;
            
            if (component.IsRecording)
            {
                GUI.color = Color.red;
                if (GUILayout.<PERSON><PERSON>("Stop"))
                {
                    component.StopRecording();
                }
            }
            else
            {
                GUI.color = Color.green;
                if (GUILayout.Button("Record"))
                {
                    component.StartRecording();
                }
            }

            GUI.color = defaultGUIColor;
        }
    }
}