using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// CoreButtonStyleElementDrawer is used when a CoreButtonStyleElement is a member variable.
    /// It hides the name field as it is unused and shows a Set Style button instead.
    /// </summary>
    [CustomPropertyDrawer(typeof(CoreButtonStyleController.CoreButtonStyleElement))]
    public class CoreButtonStyleElementDrawer : PropertyDrawer
    {
        protected const int PROPERTY_HEIGHT = 18;
        protected const int SPACE_HEIGHT = 2;
        protected const int LINE_HEIGHT = PROPERTY_HEIGHT + SPACE_HEIGHT;
    
        private bool _foldoutExpended = true;
        
        private float _propertyHeight = LINE_HEIGHT;
        
        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return _propertyHeight;
        }
        
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            _propertyHeight = 0f;
            
            _foldoutExpended = EditorGUI.Foldout(new Rect(position.x, position.y, position.width, LINE_HEIGHT), _foldoutExpended, label);
            position.y += LINE_HEIGHT;
            position.x += 15;
            position.width -= 15;
            _propertyHeight += LINE_HEIGHT;
            if (!_foldoutExpended)
            {
                return;
            }
    
            if (GUI.Button(new Rect(position.x, position.y, position.width, LINE_HEIGHT), "Set Style"))
            {
                CoreButtonStyleController.CoreButtonStyleElement styleElement = property.GetValue() as CoreButtonStyleController.CoreButtonStyleElement;
                if (styleElement != null)
                {
                    styleElement.SetStyle();
                }
            }
    
            position.y += LINE_HEIGHT;
            _propertyHeight += LINE_HEIGHT;
            
            SerializedProperty transitionPairsProperty = property.FindPropertyRelative("transitionPairs");
            float transitionPairsPropertyHeight = EditorGUI.GetPropertyHeight(transitionPairsProperty) + SPACE_HEIGHT;
            EditorGUI.PropertyField(new Rect(position.x, position.y, position.width, transitionPairsPropertyHeight), transitionPairsProperty);
            
            position.y += transitionPairsPropertyHeight;
            _propertyHeight += transitionPairsPropertyHeight;
            
            EditorGUI.EndProperty();
        }
    }
}