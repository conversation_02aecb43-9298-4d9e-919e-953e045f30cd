using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomEditor(typeof(CoreButtonEnableComponent))]
    public class CoreButtonEnableComponentEditor : CoreButtonAnimTransitionBaseEditor
    {
        private SerializedProperty _componentProperty;

        protected override void OnEnable()
        {
            base.OnEnable();
            
            _componentProperty = serializedObject.FindProperty("_component");
        }

        public override void OnInspectorGUI()
        {
            GUI.enabled = false;
            EditorGUILayout.ObjectField("Script:", MonoScript.FromMonoBehaviour((CoreButtonEnableComponent)target), typeof(CoreButtonEnableComponent), false);
            GUI.enabled = true;
            
            EditorGUILayout.PropertyField(_componentProperty);
            
            EditorGUILayout.Space();
            
            serializedObject.ApplyModifiedProperties();
            
            base.OnInspectorGUI();
        }
    }
}