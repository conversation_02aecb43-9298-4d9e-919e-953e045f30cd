using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomEditor(typeof(CoreButtonTMPColor))]
    public class CoreButtonTMPColorEditor : CoreButtonAnimTransitionBaseEditor
    {
        private SerializedProperty _tmpProperty;

        protected override void OnEnable()
        {
            base.OnEnable();
            
            _tmpProperty = serializedObject.FindProperty("_tmp");
        }

        public override void OnInspectorGUI()
        {
            GUI.enabled = false;
            EditorGUILayout.ObjectField("Script:", MonoScript.FromMonoBehaviour((CoreButtonTMPColor)target), typeof(CoreButtonTMPColor), false);
            GUI.enabled = true;
            
            EditorGUILayout.PropertyField(_tmpProperty);
            
            EditorGUILayout.Space();
            
            serializedObject.ApplyModifiedProperties();
            
            base.OnInspectorGUI();
        }
    }
}