using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomEditor(typeof(CoreButtonRectScale))]
    public class CoreButtonRectScaleEditor : CoreButtonAnimTransitionBaseEditor
    {
        private SerializedProperty _rectTransformProperty;

        protected override void OnEnable()
        {
            base.OnEnable();
            
            _rectTransformProperty = serializedObject.FindProperty("_rectTransform");
        }

        public override void OnInspectorGUI()
        {
            GUI.enabled = false;
            EditorGUILayout.ObjectField("Script:", MonoScript.FromMonoBehaviour((CoreButtonRectScale)target), typeof(CoreButtonRectScale), false);
            GUI.enabled = true;
            
            EditorGUILayout.PropertyField(_rectTransformProperty);
            
            EditorGUILayout.Space();
            
            serializedObject.ApplyModifiedProperties();
            
            base.OnInspectorGUI();
        }
    }
}