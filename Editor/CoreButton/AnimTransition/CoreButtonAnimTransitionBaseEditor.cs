using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomEditor(typeof(CoreButtonAnimTransitionBase<,,>), true)]
    public abstract class CoreButtonAnimTransitionBaseEditor : Editor
    {
        private SerializedProperty _styleProperty;
        private SerializedProperty _stateDataProperty;

        protected virtual void OnEnable()
        {
            _styleProperty = serializedObject.FindProperty("style");
            _stateDataProperty = serializedObject.FindProperty("stateData");
        }

        public override void OnInspectorGUI()
        {
            EditorGUILayout.PropertyField(_styleProperty);

            if (_styleProperty.objectReferenceValue)
            {
                SerializedObject assetObject = new SerializedObject(_styleProperty.objectReferenceValue);
                Color defaultColor = GUI.color;
                Color defaultBGColor = GUI.backgroundColor;
                GUI.color = new Color(0.7f, 0.7f, 0.7f);;
                // GUI.backgroundColor = new Color(0.3f, 0.3f, 0.3f);
                EditorGUILayout.PropertyField(assetObject.FindProperty("stateData"));
                GUI.color = defaultColor;
                GUI.backgroundColor = defaultBGColor;
            }
            else
            {
                EditorGUILayout.PropertyField(_stateDataProperty);
            }
            
            serializedObject.ApplyModifiedProperties();
        }
    }
}