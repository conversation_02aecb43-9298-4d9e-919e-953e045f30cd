using System;
using Isto.Core.UI.ButtonStyles;
using UnityEditor;
using UnityEngine;

namespace Isto.Core.UI
{
    [CustomPropertyDrawer(typeof(CoreButtonStyleController.CoreButtonStyleElement.TransitionPair))]
    public class TransitionPairDrawer : PropertyDrawer
    {
        private const int PROPERTY_HEIGHT = 18;
        private const int SPACE_HEIGHT = 2;
        private const int LABEL_WIDTH = 90;
        private const int OBJ_FIELD_WIDTH = 100;
        
        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return (PROPERTY_HEIGHT + SPACE_HEIGHT) * 2;
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            var transitionLabelRect = new Rect(position.x,
                position.y,
                LABEL_WIDTH,
                PROPERTY_HEIGHT);
            var styleLabelRect = new Rect(position.x,
                position.y + PROPERTY_HEIGHT + SPACE_HEIGHT,
                LABEL_WIDTH,
                PROPERTY_HEIGHT);
            var transitionFieldRect = new Rect(position.x + LABEL_WIDTH,
                position.y,
                position.width - LABEL_WIDTH - OBJ_FIELD_WIDTH,
                PROPERTY_HEIGHT);
            var targetObjectFieldRect = new Rect(transitionFieldRect.position.x + transitionFieldRect.width,
                position.y,
                OBJ_FIELD_WIDTH,
                PROPERTY_HEIGHT);
            var styleFieldRect = new Rect(position.x + LABEL_WIDTH,
                position.y + PROPERTY_HEIGHT + SPACE_HEIGHT,
                position.width - LABEL_WIDTH,
                PROPERTY_HEIGHT);

            SerializedProperty transitionProperty = property.FindPropertyRelative("transition");
            SerializedProperty styleProperty = property.FindPropertyRelative("style");
            
            EditorGUI.LabelField(transitionLabelRect, "Transition");
            EditorGUI.PropertyField(transitionFieldRect, transitionProperty, GUIContent.none);

            CoreButtonTransitionBase transition = null;
            transition = transitionProperty.objectReferenceValue as CoreButtonTransitionBase;
            ICoreButtonStyled transitionStyled = transition as ICoreButtonStyled;

            if (transitionStyled != null)
            {
                GameObject targetObject = transitionStyled.GetTargetObject();
                if (targetObject != null)
                {
                    using (new EditorGUI.DisabledScope(true))
                    {
                        EditorGUI.ObjectField(targetObjectFieldRect, targetObject, typeof(GameObject), true);
                    }
                }
            }

            if (transitionStyled != null)
            {
                Type type = (transition as ICoreButtonStyled).GetStyleType();
                CoreButtonStyle obj = styleProperty.objectReferenceValue as CoreButtonStyle;

                if (obj != null && (!obj.GetType().IsSubclassOf(type)))
                {
                    obj = null;
                }
                    
                EditorGUI.LabelField(styleLabelRect, "Style");
                styleProperty.objectReferenceValue = EditorGUI.ObjectField(styleFieldRect, obj, type, false);
            }

            EditorGUI.EndProperty();
        }
    }
}