#if UNITY_GAMECORE
using System.Runtime.InteropServices;

namespace FMOD
{
    public static class GameCore
    {
        public static RESULT SetXApuStreamCount(uint decodeCount, uint decodeConvertCount)
        {
            return FMOD_GameCore_SetXApuStreamCount(decodeCount, decodeConvertCount);
        }
        public static RESULT XDspConfigure(uint numberOfStreams, uint aggregateImpulseResponseInSeconds)
        {
            return FMOD_GameCore_XDspConfigure(numberOfStreams, aggregateImpulseResponseInSeconds);
        }
        public static RESULT XMAConfigure(bool enable)
        {
            return FMOD_GameCore_XMAConfigure(enable);
        }

#region importfunctions
        [DllImport(VERSION.dll)]
        private static extern RESULT FMOD_GameCore_SetXApuStreamCount(uint decodeCount, uint decodeConvertCount);
        [DllImport(VERSION.dll)]
        private static extern RESULT FMOD_GameCore_XDspConfigure(uint numberOfStreams, uint aggregateImpulseResponseInSeconds);
        [DllImport(VERSION.dll)]
        private static extern RESULT FMOD_GameCore_XMAConfigure(bool enable);
#endregion
    }
}
#endif
