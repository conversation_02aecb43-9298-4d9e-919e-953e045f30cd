using UnityEditor.Callbacks;
using System.Collections;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace I2.Loc
{
    public class PostProcessBuild_UnloadLanguages
    {
   //     [PostProcessBuild]
   //     public static void SaveGlobalSources(BuildTarget buildTarget, string pathToBuiltProject)
   //     {
			//if (LocalizationManager.Sources.Count <= 0)
			//	LocalizationManager.UpdateSources();

   //         foreach (var source in LocalizationManager.Sources.Where(x=>x.IsGlobalSource()))
   //         {
   //             source.SaveLanguages(true, PersistentStorage.eFileType.Streaming);
   //         }
   //     }

   //     [PostProcessScene]
   //     public static void SaveLocalSources()
   //     {
   //         if (EditorApplication.isPlayingOrWillChangePlaymode)
   //             return;
            
   //         LanguageSource[] sceneSources = (LanguageSource[])Resources.FindObjectsOfTypeAll(typeof(LanguageSource));
   //         foreach (var source in sceneSources.Where(x=>!x.IsGlobalSource()))
   //         {
   //             source.SaveLanguages(true, PersistentStorage.eFileType.Streaming);
   //         }
   //     }
    }
}
